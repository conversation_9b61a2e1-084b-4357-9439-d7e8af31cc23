<!--- docs
# Metadata used by our doc generator
title: Scraper Service
group: services
-->

# s2

## Login to GitLab Container Registry

```bash
az login
az acr login --name crindiebimain
```

When asked chose the `internal-cicd` subscription to have proper access to crindiebimain.
Use `app-dev` or `app-prod` for access to DLS or Machine Learning.

## Development setup

Install PostgreSQL to have pg tools locally but don't enable the postgres service

For macOS:
```bash
brew install postgresql
````

Install dependencies and pre-commit hooks:

```
poetry install --with dev
poetry run pre-commit install
```

Copy `.env_example` to `.env` and adjust it for your needs (it is used for local running in VSCode or terminal)

Run the app:

```bash
poe start
````

Explore the API docs at [http://localhost:8000/docs](http://localhost:8000/docs)

and make requests using the global API key `ApiTest123!`, for example:

```bash
curl -X POST \
 -H "Accept: application/json" \
 -H 'x-api-key: ApiTest123!' \
 "http://localhost:8000/telemetry"
````

## Building Docker images locally

### Login to GitLab Container Registry

```bash
az login
docker logout crindiebimain
az acr login --name crindiebimain
```

When asked chose the `internal-cicd` subscription to have proper access to crindiebimain.

### Make sure Docker Buildkit is enabled

For most systems set the appropriate environment variable:
```bash
export DOCKER_BUILDKIT=1
```

For macOS follow [this instruction](https://til.codeinthehole.com/posts/how-to-enable-docker-buildkit-in-macos-docker-desktop/)


### Troubleshooting
- Make sure the proper file path for POETRY_AUTH_PATH is set in the `.env` file (modify docker compose secret in case of issues with path propagation)
- If migrations fail you might need to try and remove old volumes. You can do that manually or use `docker-compose down -v` to remove all volumes
- Sometimes removing old migration images might help, you can do that by running `docker image rm s2-run-migrations:latest`


## Working with migrations

```bash
export DATABASE_URL='postgresql+psycopg2://indiebi:Password1!@localhost:5432/scraper_service_db'
alembic revision --autogenerate -m "Your message here"
```

## Two Routes, Two Auths

In the scraper-service there are endpoints that you can use only internally and those which can be possibly accessed by external customer with JWT
In main there should be two main routers:
main_internal_router and main_external_router
The convention is the same as usual - we create a router per file/module - but here it's extended.
You should create internal or/and external router in a file/module, register an endpoint to it and then register your router to either external or internal router.
Keep the naming convention as it is, so you can clearly see in merge request if you are correctly registering the router.

Example:
endpoint:
```
external_router = APIRouter(prefix="/scraper_state")

@external_router.get("/current/source/{source}", tags=[ApiTag.PUBLIC])
async def get_current_status(
    organization_id: OrganizationID, source: Source, db: StorageDependency, current_user = Depends(get_current_user)
):
    if not user_has_access_to_organization(current_user, organization_id):
        raise Unauthorized(detail="User does not have access to this organization")
    return db.get_scraper_status(organization_id, source)
```

main.py:
```
main_external_router.include_router(scraper_state_routes.external_router)
```

# Testing S2 locally with ScraperLib and Electron

1. Make sure you have `user_access_token_public_key` set in your `.env` file and your .env is loaded. Check [.env_example](.env_example) for reference.

2. Set `SCRAPER_SERVICE_URL` in Electron.

```diff
--- a/packages/shared/config/config.dev.ts
+++ b/packages/shared/config/config.dev.ts
@@ -6,7 +6,7 @@ const config = {
        DEVELOPER: true,
        ELECTRON_API_URL: 'https://electron-api.indiebi.dev',
        SCRAPER_API_URL: 'https://scraper-api.indiebi.dev',
-       SCRAPER_SERVICE_URL: 'https://scraper-service.indiebi.dev',
+       SCRAPER_SERVICE_URL: 'http://127.0.0.1:8000',
        LANDING_URL: 'https://indiebi.dev',
        MOBILE_APP_URL: 'https://mobile.indiebi.dev',
        INTERCOM_APP_ID: 'uib449m9',
```
