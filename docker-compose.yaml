services:

  app:
    build:
      context: .
      dockerfile: Dockerfile
      secrets:
        - poetry-auth
    volumes:
      - .:/app
      - ~/.azure:/home/<USER>/.azure
    init: true
    environment:
      - DOCKER_BUILDKIT=1
      - DATABASE_URL=postgresql+psycopg2://indiebi:Password1!@db:5432/scraper_service_db
      - API_KEY=ApiTest123!
    depends_on:
      run-migrations:
        condition: service_completed_successfully
    ports:
      - "8000:8000"
    # /app directory is mounted with root ownership.
    # This is a workaround to allow the app to read from the mounted volume
    user: root

  run-migrations:
    build:
      context: .
      dockerfile: Dockerfile
      secrets:
        - poetry-auth
    volumes:
      - .:/app
      - ./alembic_migrations:/app/alembic_migrations
      - ./alembic.ini:/app/alembic.ini
    init: true
    command: bash -c "alembic upgrade head"
    environment:
      - DATABASE_URL=postgresql+psycopg2://indiebi:Password1!@db:5432/scraper_service_db
    depends_on:
      db:
        condition: service_healthy

  db:
    image: postgres:17.0
    environment:
      POSTGRES_USER: indiebi
      POSTGRES_PASSWORD: Password1!
      POSTGRES_DB: scraper_service_db
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "indiebi", "-d", "scraper_service_db"]
      interval: 10s
      retries: 5
      start_period: 5s
    ports:
        - "5432:5432"
    restart: always

  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin4_container
    restart: always
    ports:
      - "8888:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: pass
    volumes:
      - pgadmin-data:/var/lib/pgadmin


secrets:
  poetry-auth:
    file: "$POETRY_AUTH_PATH" # run docker-compose via ./scripts/ensure_db_is_ready.sh. This script will set the environment variable

volumes:
  postgres-db-volume:
  pgadmin-data:
