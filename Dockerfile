FROM crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-3.12-bookworm:prod

RUN groupadd --gid 1000 service && useradd --uid 1000 --gid 1000 -r -m -g service service && mkdir -p /root/.config/pypoetry

WORKDIR /app
COPY poetry.lock pyproject.toml /app/
RUN mkdir -p /kaniko/
# if we're building with <PERSON><PERSON>, mount auth.toml as a secret
# if we're building with Kaniko and a poetry-auth.toml file is present, link it to the right spot
#   linking won't pollute the target image with auth secrets
RUN --mount=type=secret,id=poetry-auth,target=/kaniko/poetry-auth.toml \
    test -f /kaniko/poetry-auth.toml \
    && ln -sf /kaniko/poetry-auth.toml /root/.config/pypoetry/auth.toml \
    && poetry config virtualenvs.create false \
    && poetry install --without dev \
    && rm -r /root/.cache/pypoetry

COPY --chown=service:service alembic_migrations /app/alembic_migrations
COPY --chown=service:service alembic.ini /app/alembic.ini
COPY --chown=service:service scraper_service /app/scraper_service

ARG DOCKER_TAG
ENV DOCKER_TAG=$DOCKER_TAG
ENV SENTRY_RELEASE=$DOCKER_TAG

ARG DOCKER_BUILD_TIMESTAMP
ENV DOCKER_BUILD_TIMESTAMP=$DOCKER_BUILD_TIMESTAMP

USER service

CMD ["uvicorn", "scraper_service.main:app", "--host", "0.0.0.0", "--port", "8000"]
