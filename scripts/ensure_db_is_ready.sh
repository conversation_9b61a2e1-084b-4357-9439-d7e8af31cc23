#!/bin/bash

set -e # failes script if any command fails

if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    POETRY_AUTH_PATH="$HOME/.config/pypoetry/auth.toml"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    POETRY_AUTH_PATH="$HOME/Library/Application Support/pypoetry/auth.toml"
elif [[ "$OSTYPE" == "cygwin" || "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    POETRY_AUTH_PATH="$APPDATA/pypoetry/auth.toml"
else
    echo "Unsupported OS type: $OSTYPE"
    exit 1
fi

export POETRY_AUTH_PATH

docker compose up -d --scale app=0
