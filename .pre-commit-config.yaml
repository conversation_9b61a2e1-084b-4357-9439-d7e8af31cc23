repos:
  - repo: https://github.com/python-poetry/poetry
    rev: 1.8.3
    hooks:
      - id: poetry-check
        stages: [pre-push]
      - id: poetry-lock
        stages: [pre-push]
      - id: poetry-install
        stages: [pre-push]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.4.5
    hooks:
      - id: ruff
        args: [--fix]
        stages: [pre-commit]
      - id: ruff-format
        stages: [pre-commit]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        stages: [pre-commit]

      - id: end-of-file-fixer
        stages: [pre-commit]

  - repo: https://gitlab.com/bmares/check-json5
    rev: v1.0.0
    hooks:
      - id: check-json5
        name: vscode settings files
        stages: [pre-commit]
        files: .vscode/.*\.json$

  - repo: local
    hooks:
      - id: check_dead_fixtures
        name: check dead fixtures
        entry: poe check_dead_fixtures
        language: system
        pass_filenames: false
        stages: [pre-push]

  - repo: local
    hooks:
      - id: tests
        name: unit tests & integration tests
        entry: poe test
        language: system
        pass_filenames: false
        stages: [pre-push]
