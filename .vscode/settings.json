{
    "[python]": {
        "editor.codeActionsOnSave": {
            "source.fixAll": "explicit",
            "source.organizeImports": "explicit",
            "source.sortImports": "explicit"
        },
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.formatOnSave": true,
        "editor.rulers": [
            88,
        ]
    },
    "editor.formatOnSave": true,
    "editor.trimAutoWhitespace": true,
    "files.exclude": {
        "**/.pytest_cache": true,
        "**/__pycache__": true,
        "**/.ruff_cache": true,
    },
    "files.insertFinalNewline": true,
    "files.trimTrailingWhitespace": true,
    "python.analysis.autoFormatStrings": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.inlayHints.callArgumentNames": "partial",
    "python.analysis.inlayHints.functionReturnTypes": true,
    "python.analysis.inlayHints.pytestParameters": true,
    "python.analysis.inlayHints.variableTypes": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.testing.pytestArgs": [
        "-vvv",
        "--random-order",
    ],
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "ruff.lint.args": [
        // For better debugging expirience. After commenting out parts of code, vscode
        // will not remove the imports, which will be needed in case of uncommenting
        // the code. Ruff will remove unsued imports while running within a git hook
        "--unfixable=F401,F841",
    ],
    "workbench.colorCustomizations": {
        "activityBar.activeBackground": "#ffffff",
        "activityBar.background": "#ffffff",
        "activityBar.foreground": "#15202b",
        "activityBar.inactiveForeground": "#15202b99",
        "activityBarBadge.background": "#df9f9f",
        "activityBarBadge.foreground": "#15202b",
        "commandCenter.border": "#15202b99",
        "sash.hoverBorder": "#ffffff",
        "statusBar.background": "#f1fbfd",
        "statusBar.foreground": "#15202b",
        "statusBarItem.hoverBackground": "#c4eef7",
        "statusBarItem.remoteBackground": "#f1fbfd",
        "statusBarItem.remoteForeground": "#15202b",
        "titleBar.activeBackground": "#f1fbfd",
        "titleBar.activeForeground": "#15202b",
        "titleBar.inactiveBackground": "#f1fbfd99",
        "titleBar.inactiveForeground": "#15202b99"
    },
    "peacock.color": "#f1fbfd"
}
