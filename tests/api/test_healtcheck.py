from unittest import mock

from sqlalchemy.exc import SQLAlchemyError


def test_health_returns_ok(api_key_client):
    response = api_key_client.get("health")

    assert response.status_code == 200


def test_synthetic_health_check_returns_storage_not_connected_when_container_does_not_exist(
    api_key_client,
):
    response = api_key_client.get("/health/synthetic")

    assert response.status_code == 200

    assert response.json()["database_connected"] is True
    assert response.json()["database_duration"] is not None


@mock.patch("scraper_service.api.routes.healthcheck.select")
def test_synthetic_health_check_returns_database_not_connected_on_db_error(
    mock_select, api_key_client
):
    mock_select.side_effect = SQLAlchemyError()
    response = api_key_client.get("/health/synthetic")

    assert response.status_code == 200

    assert response.json()["database_connected"] is False
    assert response.json()["database_duration"] is None
