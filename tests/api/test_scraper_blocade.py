from sqlalchemy import select

from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.events.event import _EventModel
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedEvent
from scraper_service.logic.repositories.scraper_status import _ScraperStatusModel


async def test_blocking_scraper_for_not_existing_status_will_return_404(
    async_api_key_client,
    test_organization_id,
    test_user_id,
):
    body = {
        "organization_id": test_organization_id,
        "user_id": test_user_id,
        "source": "epic_sales",
    }

    response = await async_api_key_client.post("/scraper/block", json=body)

    assert response.status_code == 404, response.text
    assert response.json() == {"detail": "Scraper not configured"}


async def test_blocking_existing_scraper_status_will_set_scraper_status_to_manually_blocked(
    async_jwt_client,
    async_api_key_client,
    scraper_state_changed_event_factory,
    async_session,
    test_user_id,
    test_organization_id,
) -> None:
    # prepare exiting scraper status
    event: ScraperStateChangedEvent = scraper_state_changed_event_factory(
        organization_id=test_organization_id,
        source="steam_sales",
    )
    response = await async_jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )
    assert response.status_code == 200

    # set blockade
    body = {
        "organization_id": test_organization_id,
        "user_id": test_user_id,
        "source": "steam_sales",
    }
    response = await async_api_key_client.post("/scraper/block", json=body)

    assert response.status_code == 200
    assert response.json() is None

    db_event = (await async_session.execute(select(_EventModel))).scalars().all()

    assert len(db_event) == 2

    db_status = (
        (await async_session.execute(select(_ScraperStatusModel))).scalars().one()
    )
    assert db_status.state == "MANUALLY_BLOCKED"
    assert db_status.organization_id == test_organization_id
    assert db_status.user_id == test_user_id


async def test_unblocking_scraper_for_not_existing_status_will_return_404(
    async_api_key_client,
    test_organization_id,
    test_user_id,
):
    body = {
        "organization_id": test_organization_id,
        "user_id": test_user_id,
        "source": "epic_sales",
    }

    response = await async_api_key_client.post("/scraper/unblock", json=body)

    assert response.status_code == 404, response.text
    assert response.json() == {"detail": "Scraper not configured"}


async def test_unblocking_existing_scraper_status_will_set_scraper_status_to_manually_unblocked(
    async_jwt_client,
    async_api_key_client,
    scraper_state_changed_event_factory,
    async_session,
    test_organization_id,
    test_user_id,
) -> None:
    # prepare exiting scraper status
    event: ScraperStateChangedEvent = scraper_state_changed_event_factory(
        organization_id=test_organization_id,
        source="steam_sales",
    )
    response = await async_jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )
    assert response.status_code == 200

    # set blockade
    body = {
        "organization_id": test_organization_id,
        "user_id": test_user_id,
        "source": "steam_sales",
    }
    response = await async_api_key_client.post("/scraper/block", json=body)

    # release blockade
    response = await async_api_key_client.post("/scraper/unblock", json=body)

    db_event = (await async_session.execute(select(_EventModel))).scalars().all()

    assert len(db_event) == 3

    db_status = (
        (await async_session.execute(select(_ScraperStatusModel))).scalars().one()
    )
    assert db_status.state == "MANUALLY_UNBLOCKED"


async def test_blocking_existing_scraper_status_will_block_status_updates(
    async_jwt_client,
    async_api_key_client,
    scraper_state_changed_event_factory,
    async_session,
    test_organization_id,
    test_user_id,
) -> None:
    # prepare exiting scraper status
    event: ScraperStateChangedEvent = scraper_state_changed_event_factory(
        organization_id=test_organization_id,
        source="steam_sales",
    )
    response = await async_jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )
    assert response.status_code == 200

    # set blockade
    body = {
        "organization_id": test_organization_id,
        "user_id": test_user_id,
        "source": "steam_sales",
    }
    response = await async_api_key_client.post("/scraper/block", json=body)

    assert response.status_code == 200
    assert response.json() is None

    # try to change scraper state to failed
    event.body.new_state = ScraperBinaryStatus.FAILED
    response = await async_api_key_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )
    assert response.status_code == 200, "We should allow to save new events"

    db_event = (await async_session.execute(select(_EventModel))).scalars().all()

    assert len(db_event) == 3

    db_status = (
        (await async_session.execute(select(_ScraperStatusModel))).scalars().one()
    )
    assert db_status.state == "MANUALLY_BLOCKED"
