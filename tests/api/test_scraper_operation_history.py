from scraper_service.logic.entities import ScraperBinaryStatus


def test_should_get_all_single_operation_history(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
    test_organization_id,
    test_user_id,
):
    event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test"
    )
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())

    response = api_key_client.get("/scraper_operation_history")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["state"] == ScraperBinaryStatus.STARTED
    assert response_json[0]["updated_at"] is not None
    assert response_json[0]["organization_id"] == test_organization_id
    assert response_json[0]["source"] == "steam_sales"
    assert response_json[0]["operation_id"] == "test"
    assert response_json[0]["fail_reason"] is None
    assert response_json[0]["user_id"] == test_user_id


def test_should_get_multiple_operation_history(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
):
    event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test"
    )
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())
    event_2 = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test2"
    )
    jwt_client.post("/external/scraper_events", content=event_2.model_dump_json())
    response = api_key_client.get("/scraper_operation_history")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 2


def test_should_get_single_operation_when_modified(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
):
    event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test"
    )
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())
    event_2 = scraper_state_changed_event_factory(state="FINISHED", operation_id="test")
    jwt_client.post("/external/scraper_events", content=event_2.model_dump_json())
    response = api_key_client.get("/scraper_operation_history")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1


def test_should_get_fail_reason_on_fail(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
):
    event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test"
    )
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())
    event_2 = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.FAILED, operation_id="test", reason="fail reason"
    )
    jwt_client.post("/external/scraper_events", content=event_2.model_dump_json())
    response = api_key_client.get("/scraper_operation_history")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["fail_reason"] == "fail reason"


def test_should_get_multiple_operation_history_for_single_organization(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
    test_organization_id,
):
    event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test"
    )
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())
    event_2 = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test2"
    )
    jwt_client.post("/external/scraper_events", content=event_2.model_dump_json())
    event_3 = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test2", organization_id="other"
    )
    jwt_client.post("/external/scraper_events", content=event_3.model_dump_json())
    response = api_key_client.get(
        "/scraper_operation_history",
        params={"organization_id": test_organization_id},
    )

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 2
    assert all(row["organization_id"] == test_organization_id for row in response_json)


def test_should_get_all_rows_for_org_source_set(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
    test_organization_id,
):
    event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test"
    )
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())
    event_2 = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test2", source="nintendo_sales"
    )
    jwt_client.post("/external/scraper_events", content=event_2.model_dump_json())
    event_3 = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="test2", organization_id="other"
    )
    jwt_client.post("/external/scraper_events", content=event_3.model_dump_json())
    response = api_key_client.get(
        "/scraper_operation_history",
        params={"organization_id": test_organization_id, "source": "steam_sales"},
    )

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["organization_id"] == test_organization_id
    assert response_json[0]["source"] == "steam_sales"


def test_login_should_be_visible_in_history(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
    login_state_changed_event_factory,
    test_organization_id,
):
    failed_login_event = login_state_changed_event_factory(
        body__new_state="FAILED",
        body__reason="INCORRECT_CREDENTIALS",
        operation_id="0",
    )
    jwt_client.post(
        "/external/scraper_events", content=failed_login_event.model_dump_json()
    )

    login_event = login_state_changed_event_factory(operation_id="1")
    jwt_client.post("/external/scraper_events", content=login_event.model_dump_json())

    scrape_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, operation_id="2"
    )
    jwt_client.post("/external/scraper_events", content=scrape_event.model_dump_json())

    response = api_key_client.get(
        "/scraper_operation_history",
        params={"organization_id": test_organization_id, "source": "steam_sales"},
    )

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 3

    assert response_json[0]["state"] == "FAILED"
    assert response_json[0]["operation_id"] == "0"
    assert response_json[0]["fail_reason"] == "INCORRECT_CREDENTIALS"

    assert response_json[1]["state"] == "CONFIGURED"
    assert response_json[1]["operation_id"] == "1"

    assert response_json[2]["state"] == "STARTED"
    assert response_json[2]["operation_id"] == "2"
