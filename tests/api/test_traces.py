import base64
import json

from scraper_service.logs import configure_logger


def test_returns_ok_on_empty_trace(jwt_client):
    response = jwt_client.put("external/traces", json=[])

    assert response.status_code == 200


def test_returns_ok_on_simple_trace(jwt_client, capsys):
    configure_logger()

    response = jwt_client.put(
        "external/traces",
        json=[
            {
                "message": base64.b64encode(b"Info implicit").decode("utf-8"),
                "client_timestamp": "2021-01-01T00:00:00",
                "user_id": "u-tWCKyO",
                "organization_id": "o-yyPepo",
                "origin": "scraper-lib-2.3.4",
            },
            {
                "message": base64.b64encode(b"Info explicit").decode("utf-8"),
                "level": 1,
                "client_timestamp": "2021-01-01T00:00:05",
                "user_id": "u-tWCKyO",
                "organization_id": "o-yyPepo",
                "origin": "scraper-lib-2.3.4",
            },
            {
                "message": base64.b64encode(b"Warning").decode("utf-8"),
                "level": 2,
                "client_timestamp": "2021-01-01T00:00:05",
                "user_id": "u-tWCKyO",
                "organization_id": "o-yyPepo",
                "origin": "scraper-lib-2.3.4",
            },
            {
                "message": base64.b64encode(b"Error").decode("utf-8"),
                "level": 3,
                "client_timestamp": "2021-01-01T00:00:05",
                "user_id": "u-tWCKyO",
                "organization_id": "o-yyPepo",
                "origin": "scraper-lib-2.3.4",
            },
        ],
    )

    captured = capsys.readouterr()
    assert response.status_code == 200
    assert [json.loads(record)["message"] for record in captured.out.splitlines()] == [
        "Info implicit",
        "Info explicit",
        "Warning",
        "Error",
    ]


def test_save_trace_with_jwt(jwt_client, capsys):
    configure_logger()

    response = jwt_client.put(
        "external/traces",
        json=[
            {
                "message": base64.b64encode(b"Info implicit").decode("utf-8"),
                "client_timestamp": "2021-01-01T00:00:00",
                "user_id": "wrong_user",
                "organization_id": "wrong_org",
                "origin": "scraper-lib-2.3.4",
            }
        ],
    )

    assert response.status_code == 200
    captured = capsys.readouterr()
    assert captured.out
    for record in [json.loads(line) for line in captured.out.splitlines()]:
        assert record["organization_id"] == "o-yyPepo"
        assert record["user_id"] == "u-tWCKyO"


def test_save_trace_with_jwt_and_machine_info(jwt_client, capsys, test_machine_info):
    configure_logger()

    response = jwt_client.put(
        "external/traces",
        json=[
            {
                "message": base64.b64encode(b"Info implicit").decode("utf-8"),
                "client_timestamp": "2021-01-01T00:00:00",
                "user_id": "wrong_user",
                "organization_id": "wrong_org",
                "origin": "scraper-lib-2.3.4",
                "machine_info": test_machine_info,
            }
        ],
    )

    assert response.status_code == 200
    captured = capsys.readouterr()
    assert captured.out
    for record in [json.loads(line) for line in captured.out.splitlines()]:
        assert record["organization_id"] == "o-yyPepo"
        assert record["user_id"] == "u-tWCKyO"
        assert record["s2"]["machine_info"] == test_machine_info


def test_save_trace_with_standard_and_non_standard_custom_fields(jwt_client, capsys):
    configure_logger()

    response = jwt_client.put(
        "external/traces",
        json=[
            {
                "message": base64.b64encode(b"Info implicit").decode("utf-8"),
                "client_timestamp": "2021-01-01T00:00:00",
                "user_id": "wrong_user",
                "organization_id": "wrong_org",
                "origin": "scraper-lib-2.3.4",
                "custom_fields": base64.b64encode(
                    json.dumps({
                        "extra_field": "value",
                        "manager_version": "1.0",
                    }).encode("utf-8")
                ).decode("utf-8"),
            }
        ],
    )

    assert response.status_code == 200
    captured = capsys.readouterr()
    assert captured.out
    for record in [json.loads(line) for line in captured.out.splitlines()]:
        assert record["s2"]["nonstandard_custom_fields"] == '{"extra_field": "value"}'
        assert record["s2"]["manager_version"] == "1.0"
