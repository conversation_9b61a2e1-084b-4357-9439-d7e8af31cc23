import base64
import json

import pytest

from scraper_service.api.routes.traces import decode_custom_fields


@pytest.mark.parametrize(
    ("encoded_custom_fields", "expected_standardized", "expected_additional"),
    [
        (
            base64.b64encode(
                json.dumps({"manager_version": "1.0", "extra_field": "value"}).encode(
                    "utf-8"
                )
            ).decode("utf-8"),
            {"manager_version": "1.0"},
            '{"extra_field": "value"}',
        ),
        (
            base64.b64encode(
                json.dumps({"manager_version": "2.0"}).encode("utf-8")
            ).decode("utf-8"),
            {"manager_version": "2.0"},
            None,
        ),
        (
            base64.b64encode(
                json.dumps({"extra_field": "value"}).encode("utf-8")
            ).decode("utf-8"),
            {},
            '{"extra_field": "value"}',
        ),
    ],
)
def test_decode_custom_fields_correctly_extracts_standardized_and_additional_fields(
    encoded_custom_fields, expected_standardized, expected_additional
):
    standardized, additional = decode_custom_fields(encoded_custom_fields)
    assert standardized == expected_standardized
    assert additional == expected_additional


@pytest.mark.parametrize(
    "encoded_custom_fields",
    [
        None,
        "",
    ],
)
def test_decode_custom_fields_returns_empty_dict_and_none_for_empty_or_none_input(
    encoded_custom_fields,
):
    standardized, additional = decode_custom_fields(encoded_custom_fields)
    assert standardized == {}
    assert additional is None


@pytest.mark.parametrize(
    ("invalid_encoded_custom_fields", "expected_error_match"),
    [
        ("not_base64", "Invalid base64-encoded string"),
        (
            base64.b64encode(b"invalid_json").decode("utf-8"),
            "Expecting value: line 1 column 1",
        ),
    ],
)
def test_decode_custom_fields_raises_value_error_for_invalid_base64_or_json(
    invalid_encoded_custom_fields, expected_error_match
):
    with pytest.raises(ValueError, match=expected_error_match):
        decode_custom_fields(invalid_encoded_custom_fields)
