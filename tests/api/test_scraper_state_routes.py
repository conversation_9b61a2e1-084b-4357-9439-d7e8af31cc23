from datetime import UTC, datetime

from freezegun import freeze_time
from sqlalchemy import select

from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.repositories.scraper_status import _ScraperStatusModel


def test_should_get_all_statuses(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
    test_organization_id,
):
    event = scraper_state_changed_event_factory()
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())

    response = api_key_client.get("/scraper_state/all")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["state"] == ScraperBinaryStatus.STARTED
    assert response_json[0]["updated_at"] is not None
    assert response_json[0]["organization_id"] == test_organization_id
    assert response_json[0]["last_origin"] is not None
    assert response_json[0]["source"] == "steam_sales"


def test_should_get_all_organization_statuses(
    jwt_client,
    api_key_client,
    scraper_state_changed_event_factory,
    test_organization_id,
):
    event = scraper_state_changed_event_factory()
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())

    event = scraper_state_changed_event_factory(organization_id="o-not-this")
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())

    response = api_key_client.get(
        "/scraper_state/all", params={"organization_id": test_organization_id}
    )

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["organization_id"] == test_organization_id


def test_should_return_empty_array_for_accessible_organization(
    jwt_client,
    jwt_token_accesible_organization,
    test_user_id,
):
    response = jwt_client.get(
        "external/scraper_state/current/my",
        params={
            "organization_id": jwt_token_accesible_organization,
            "user_id": test_user_id,
        },
    )

    assert response.status_code == 200
    response_json = response.json()
    assert response_json == []


def test_should_return_running_status_for_steam_for_accessible_organization(
    jwt_client,
    jwt_token_accesible_organization,
    test_user_id,
    scraper_state_changed_event_factory,
):
    event = scraper_state_changed_event_factory()
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())
    response = jwt_client.get(
        "external/scraper_state/current/my",
        params={
            "organization_id": jwt_token_accesible_organization,
            "user_id": test_user_id,
        },
    )

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    [status] = response_json
    assert status["state"] == "STARTED"
    assert status["source"] == "steam_sales"
    assert status["user_id"] == test_user_id
    assert status["organization_id"] == jwt_token_accesible_organization
    assert status["user_email"] == "<EMAIL>"
    assert status["organization_name"] == "Test Org"


async def test_scraper_state_updated_at_is_changed_correctly(
    async_jwt_client, scraper_state_changed_event_factory, async_session
):
    event = scraper_state_changed_event_factory()

    with freeze_time("2023-01-01 12:00:00", ignore=["jwt"]):
        await async_jwt_client.post(
            "/external/scraper_events", content=event.model_dump_json()
        )
        db_scraper_status = (
            (await async_session.execute(select(_ScraperStatusModel))).scalars().one()
        )
        assert db_scraper_status.updated_at == datetime(
            2023, 1, 1, 12, 00, 00, tzinfo=UTC
        )

    with freeze_time("2023-01-01 13:00:00", ignore=["jwt"]):
        await async_jwt_client.post(
            "/external/scraper_events", content=event.model_dump_json()
        )
        db_scraper_status = (
            (await async_session.execute(select(_ScraperStatusModel))).scalars().one()
        )
        assert db_scraper_status.updated_at == datetime(
            2023, 1, 1, 13, 00, 00, tzinfo=UTC
        )


def test_successful_credentials_login_sets_scraper_as_configured_and_is_manual_is_false(
    jwt_client,
    api_key_client,
    login_state_changed_event_factory,
    test_organization_id,
):
    event = login_state_changed_event_factory(body__is_manual=False)
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())

    response = api_key_client.get("/scraper_state/all")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["state"] == ScraperBinaryStatus.CONFIGURED
    assert response_json[0]["organization_id"] == test_organization_id
    assert response_json[0]["source"] == "steam_sales"
    assert response_json[0]["uses_manual_session"] == False
    assert (
        response_json[0]["last_success_timestamp"] is None
    ), "successful timestamp relates only to scraping"


def test_failed_login_sets_scraper_as_failed(
    jwt_client,
    api_key_client,
    login_state_changed_event_factory,
    test_organization_id,
):
    event = login_state_changed_event_factory(
        body__new_state="FAILED",
        body__reason="INCORRECT_CREDENTIALS",
    )
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())

    response = api_key_client.get("/scraper_state/all")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["state"] == ScraperBinaryStatus.FAILED
    assert response_json[0]["organization_id"] == test_organization_id
    assert response_json[0]["source"] == "steam_sales"
    assert response_json[0]["last_fail_timestamp"] is not None
    assert response_json[0]["last_fail_reason"] == "INCORRECT_CREDENTIALS"
    assert response_json[0]["last_command"] == "LOGIN"
    assert response_json[0]["consecutive_failed_scrape_count"] == 1
