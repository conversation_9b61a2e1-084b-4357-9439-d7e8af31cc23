def test_successful_login_sets_scraper_as_configured(
    jwt_client,
    api_key_client,
    login_state_changed_event_factory,
    any_string_date,
):
    event = login_state_changed_event_factory()
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())

    response = api_key_client.get("/login_state/all")

    assert response.status_code == 200
    assert response.json() == [
        {
            "organization_id": "o-yyPepo",
            "user_id": "u-tWCKyO",
            "source": "steam_sales",
            "state": "CONFIGURED",
            "created_at": any_string_date,
            "updated_at": any_string_date,
            "is_manual_session": False,
            "consecutive_failed_login_count": 0,
            "last_success_timestamp": any_string_date,
            "last_fail_timestamp": None,
            "last_fail_reason": None,
            "last_origin": "fake-1.0.0",
            "last_operation_id": "login_operation_123",
            "version": 0,
        }
    ]


def test_failed_login_sets_scraper_as_configured(
    jwt_client,
    api_key_client,
    login_state_changed_event_factory,
    any_string_date,
):
    event = login_state_changed_event_factory(
        body__new_state="FAILED",
        body__reason="INCORRECT_CREDENTIALS",
        operation_id="0",
    )
    jwt_client.post("/external/scraper_events", content=event.model_dump_json())

    response = api_key_client.get("/login_state/all")

    assert response.status_code == 200
    status = response.json()[0]
    assert status["state"] == "FAILED"
    assert status["last_fail_timestamp"] == any_string_date
    assert status["last_fail_reason"] == "INCORRECT_CREDENTIALS"
    assert status["consecutive_failed_login_count"] == 1
    assert status["last_success_timestamp"] is None
