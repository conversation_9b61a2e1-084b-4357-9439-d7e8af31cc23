import pytest

# public endpoints


@pytest.mark.parametrize("url", ["/health", "/health/synthetic"])
def test_healthcheck_does_not_require_auth_header(url, unauthenticated_client):
    response = unauthenticated_client.get(url)
    assert response.status_code == 200


# private endpoints


def test_endpoints_rejects_requests_without_auth_header(unauthenticated_client):
    response = unauthenticated_client.get("/scraper_state/all")
    assert response.status_code == 403
    assert response.json() == {"detail": "Not authenticated"}


def test_endpoints_accept_requests_with_valid_auth_header(api_key_client):
    response = api_key_client.get("/scraper_state/all")
    assert response.status_code == 200


def test_endpoints_rejects_requests_with_invalid_api_key(unauthenticated_client):
    response = unauthenticated_client.get(
        "/scraper_state/all", headers={"x-api-key": "test"}
    )
    assert response.status_code == 403
    assert response.json()["detail"] == "Invalid API key"


def test_endpoints_accept_requests_with_valid_api_key(unauthenticated_client):
    response = unauthenticated_client.get(
        "/scraper_state/all", headers={"x-api-key": "ApiTest123!"}
    )
    assert response.status_code == 200


# external endpoints


def test_endpoints_accept_requests_with_valid_jwt_header(jwt_client):
    response = jwt_client.post("/external/scraper_events")
    assert response.status_code == 422


def test_endpoints_rejects_requests_with_missing_jwt(unauthenticated_client):
    response = unauthenticated_client.post("/external/scraper_events")
    assert response.status_code == 403
    assert response.json()["detail"] == "Missing JWT"


def test_endpoints_rejects_requests_with_invalid_jwt(unauthenticated_client):
    response = unauthenticated_client.post(
        "/external/scraper_events", headers={"Authorization": "Bearer test"}
    )
    assert response.status_code == 403
    assert (
        response.json()["detail"]
        == "TokenValidationError: DecodeError('Not enough segments')"
    )
