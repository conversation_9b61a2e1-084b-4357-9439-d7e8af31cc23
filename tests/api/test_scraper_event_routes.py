import datetime

from freezegun import freeze_time
from sqlalchemy import select

from scraper_service.logic.events.event import _EventModel
from scraper_service.logic.repositories.scraper_status import _ScraperStatusModel


def test_should_only_accept_scraper_events(jwt_client):
    response = jwt_client.post(
        "/external/scraper_events", json={"event_type": "Im lost "}
    )

    assert response.status_code == 422


def test_should_post_new_scraper_event(jwt_client, scraper_state_changed_event_factory):
    event = scraper_state_changed_event_factory()

    response = jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )

    assert response.status_code == 200
    assert response.json() is None


def test_can_post_new_event_for_accesible_organization(
    jwt_client, scraper_state_changed_event_factory, jwt_token_accesible_organization
):
    event = scraper_state_changed_event_factory(
        organization_id=jwt_token_accesible_organization
    )

    response = jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )

    assert response.status_code == 200
    assert response.json() is None


async def test_jwt_client_posts_jwt_user_in_event(
    async_jwt_client,
    scraper_state_changed_event_factory,
    async_session,
    jwt_token_accesible_organization,
):
    event = scraper_state_changed_event_factory(
        organization_id=jwt_token_accesible_organization, user_id="wrong user"
    )

    response = await async_jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )

    assert response.status_code == 200
    assert response.json() is None

    db_event = (
        (await async_session.execute(select(_ScraperStatusModel))).scalars().one()
    )
    assert db_event.user_id == "u-tWCKyO"


async def test_event_with_provided_machine_info_is_stored_with_it(
    async_jwt_client,
    scraper_state_changed_event_factory,
    async_session,
    jwt_token_accesible_organization,
    test_machine_info,
):
    event = scraper_state_changed_event_factory(
        organization_id=jwt_token_accesible_organization, user_id="wrong user"
    )

    response = await async_jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )

    assert response.status_code == 200
    assert response.json() is None
    db_event = (await async_session.execute(select(_EventModel))).scalars().one()
    assert db_event.body["machine_info"] == test_machine_info


async def test_expired_jwt_client_posts_uses_user_from_token(
    async_almost_expired_jwt_client,
    scraper_state_changed_event_factory,
    async_session,
    jwt_token_accesible_organization,
):
    event = scraper_state_changed_event_factory(
        organization_id="wrong organization", user_id="wrong user"
    )

    response = await async_almost_expired_jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )

    assert response.status_code == 200
    assert response.json() is None

    db_event = (
        (await async_session.execute(select(_ScraperStatusModel))).scalars().one()
    )
    assert db_event.user_id == "u-tWCKyO"
    assert db_event.organization_id == jwt_token_accesible_organization


async def test_event_received_at_is_set_correctly(
    async_jwt_client, scraper_state_changed_event_factory, async_session
):
    event = scraper_state_changed_event_factory()

    with freeze_time("2023-01-01 12:00:00", ignore=["jwt"]):
        await async_jwt_client.post(
            "/external/scraper_events", content=event.model_dump_json()
        )

    with freeze_time("2023-01-01 13:00:00", ignore=["jwt"]):
        await async_jwt_client.post(
            "/external/scraper_events", content=event.model_dump_json()
        )
    db_events = (await async_session.execute(select(_EventModel))).scalars().all()
    assert db_events[0].received_at == datetime.datetime(
        2023, 1, 1, 12, 00, 00, tzinfo=datetime.UTC
    )
    assert db_events[1].received_at == datetime.datetime(
        2023, 1, 1, 13, 00, 00, tzinfo=datetime.UTC
    )
