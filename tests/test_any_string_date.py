import pytest


@pytest.mark.parametrize(
    "date_string",
    [
        "2023-10-01T12:00:00Z",
        "2023-10-01T12:00:00+00:00",
        "2023-10-01T12:00:00.123456Z",
        "2023-10-01T12:00:00.123456+00:00",
    ],
)
def test_any_string_date_returns_true_compared_with_valid_dates(
    date_string, any_string_date
):
    assert date_string == any_string_date


@pytest.mark.parametrize(
    "invalid_date_string",
    [
        None,
        "random_text",
        "",
        "2023-46-01T12:00:00Z",
    ],
)
def test_any_string_date_returns_false_compared_with_invalid_dates(
    invalid_date_string, any_string_date
):
    assert invalid_date_string != any_string_date
