import importlib
import json
import logging
import logging.config
from pathlib import Path

import pytest

from scraper_service.logs import configure_logger


@pytest.fixture(autouse=True)
def _reset_logger():
    def _reset_logger():
        logging.shutdown()
        importlib.reload(logging)
        importlib.reload(logging.config)

    _reset_logger()
    yield
    _reset_logger()


def test_configured_logger_logs_to_stdout(caplog, capsys):
    configure_logger()

    logging.getLogger("test").info("Oh, hi Mark!")

    captured = capsys.readouterr()
    assert len(captured.out) != 0
    assert len(captured.err) == 0
    assert len(caplog.records) == 0


def test_configured_logger_logs_ecs_formatted_messages(capsys):
    configure_logger()
    test_msg = "Oh, hi Mark!"
    logging.getLogger("test").info(test_msg)
    captured = capsys.readouterr()
    assert captured.out
    assert len(captured.out.splitlines()) == 1
    message = json.loads(captured.out)
    assert message["@timestamp"] is not None
    assert message["log.level"] == "info"
    assert message["message"] == test_msg
    assert message["log"]["origin"]["file"]["name"] == Path(__file__).name
    assert (
        message["log"]["origin"]["function"]
        == "test_configured_logger_logs_ecs_formatted_messages"
    )
    assert message["log"]["logger"] == "test"


def test_binaries_output_logger_do_not_print_duplicates(capsys):
    configure_logger()
    test_msg = "Oh, hi Mark!"
    logger = logging.getLogger("binaries_output_logger")
    logger.info(test_msg)

    captured = capsys.readouterr()
    assert len(captured.out.splitlines()) == 1
