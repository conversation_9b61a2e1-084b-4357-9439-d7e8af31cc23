import time
from datetime import datetime, timedelta

import jwt
import pytest

from scraper_service.api.auth import (
    TokenValidationError,
    get_user_from_jwt,
    validate_token,
)


def test_validate_token_ignores_expired_token(
    user_access_token_private_key, user_access_token_public_key
):
    now_timestamp = int(time.time())
    token_contents = {
        "iat": now_timestamp - 24 * 60 * 60,
        "exp": now_timestamp - 23 * 60 * 60,
    }
    expired_token = jwt.encode(
        token_contents, user_access_token_private_key, algorithm="RS256"
    )
    validate_token(expired_token, user_access_token_public_key)


def test_validate_token_accepts_valid_token(
    user_access_token_private_key, user_access_token_public_key
):
    now_timestamp = int(time.time())
    token_contents = {
        "exp": now_timestamp + 2 * 60 * 60,
        "iat": now_timestamp - 2 * 60 * 60,
    }
    expired_token = jwt.encode(
        token_contents, user_access_token_private_key, algorithm="RS256"
    )
    validate_token(expired_token, user_access_token_public_key)


def test_validate_token_rejects_invalid_token(
    user_access_token_private_key, user_access_token_public_key
):
    with pytest.raises(TokenValidationError):
        validate_token("foobar", user_access_token_public_key)


@pytest.fixture
def token_content() -> dict:
    return {
        "id": 1,
        "email": "<EMAIL>",
        "user_id": "u-iVi2pB",
        "permissions": [
            {
                "organization_id": "o-EGQfNf",
                "name": "DESKTOP_APP_LEGACY_PARENT",
                "filters": [],
            }
        ],
        "exp": 2840390825,  # 2060-01-03T21:27:05Z
        "iat": 1740387225,
        "iss": "indiebi:user_service",
        "impersonator_app_id": "4ad7df87-6c81-44d4-ac4e-1563cc905b21",
        "impersonator_email": "<EMAIL>",
    }


@pytest.fixture
def valid_token(token_content, user_access_token_private_key) -> str:
    valid_token = jwt.encode(
        token_content, user_access_token_private_key, algorithm="RS256"
    )
    return valid_token


@pytest.fixture
def almost_expired_token(token_content, user_access_token_private_key) -> str:
    token_content["exp"] = int(
        (datetime.now() - timedelta(hours=2, minutes=59)).timestamp()
    )
    return jwt.encode(token_content, user_access_token_private_key, algorithm="RS256")


@pytest.fixture
def expired_token(token_content, user_access_token_private_key) -> str:
    token_content["exp"] = int(
        (datetime.now() - timedelta(hours=3, minutes=1)).timestamp()
    )
    return jwt.encode(token_content, user_access_token_private_key, algorithm="RS256")


@pytest.fixture
def token_without_valid_permission(token_content, user_access_token_private_key) -> str:
    token_content["permissions"] = [
        {"organization_id": "o-EGQfNf", "name": "NO_VALID_PERMISSION", "filters": []}
    ]
    return jwt.encode(token_content, user_access_token_private_key, algorithm="RS256")


def test_get_user_from_valid_jwt_returns_user(
    valid_token, user_access_token_public_key
):
    user_from_jwt = get_user_from_jwt(valid_token, user_access_token_public_key)

    assert user_from_jwt is not None
    assert user_from_jwt["main_organization_id"] == "o-EGQfNf"


def test_get_user_from_almost_expired_jwt_returns_user(
    almost_expired_token, user_access_token_public_key
):
    user_from_jwt = get_user_from_jwt(
        almost_expired_token, user_access_token_public_key
    )

    assert user_from_jwt is not None
    assert user_from_jwt["main_organization_id"] == "o-EGQfNf"


def test_get_user_from_raises_exception_after_3_hours_token_expires(
    expired_token, user_access_token_public_key
):
    with pytest.raises(TokenValidationError) as e:
        get_user_from_jwt(expired_token, user_access_token_public_key)

    assert str(e.value) == "TokenValidationError: Token expired"


def test_get_user_from_jwt_without_permission_raises_exception(
    token_without_valid_permission, user_access_token_public_key
):
    with pytest.raises(TokenValidationError) as e:
        get_user_from_jwt(token_without_valid_permission, user_access_token_public_key)
    assert (
        str(e.value)
        == "TokenValidationError: User does not have access to any organizations."
    )
