import pytest

from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.models.scraper_operation_history import (
    ScraperOperationHistory,
)


def test_should_have_default_state():
    history_record = ScraperOperationHistory(
        organization_id="123", source="steam_sales", operation_id="123", user_id="123"
    )

    assert history_record.state == ScraperBinaryStatus.UNCONFIGURED


@pytest.mark.parametrize(
    ("input_state", "expected_state"),
    [
        (ScraperBinaryStatus.STARTED, ScraperBinaryStatus.STARTED),
        (ScraperBinaryStatus.FINISHED, ScraperBinaryStatus.FINISHED),
        (ScraperBinaryStatus.FAILED, ScraperBinaryStatus.FAILED),
        (ScraperBinaryStatus.STOPPED, ScraperBinaryStatus.STOPPED),
    ],
)
def test_should_have_state_of_last_event(
    input_state,
    expected_state,
    scraper_state_changed_event_factory,
    scraping_operation_history_factory,
):
    # Given
    scraper_operation = scraping_operation_history_factory()
    event = scraper_state_changed_event_factory(state=input_state)

    # When
    scraper_operation.apply(event)

    # Then
    assert scraper_operation.state == expected_state


@pytest.mark.parametrize(
    ("ending_state"),
    [
        ScraperBinaryStatus.FINISHED,
        ScraperBinaryStatus.FAILED,
        ScraperBinaryStatus.STOPPED,
    ],
)
def test_should_display_proper_start_end_date(
    ending_state,
    scraper_state_changed_event_factory,
    scraping_operation_history_factory,
):
    scraper_operation = scraping_operation_history_factory(operation_id="123")
    start_date = "2021-02-01T00:00:00Z"
    end_date = "2021-02-02T00:00:00Z"
    started_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED, received_at=start_date, operation_id="123"
    )
    ending_event = scraper_state_changed_event_factory(
        state=ending_state, received_at=end_date, operation_id="123"
    )

    scraper_operation.apply(started_event)
    scraper_operation.apply(ending_event)

    assert scraper_operation.start_timestamp == started_event.received_at
    assert scraper_operation.end_timestamp == ending_event.received_at
    assert scraper_operation.state == ending_state
    assert scraper_operation.operation_id == started_event.operation_id
    assert scraper_operation.user_id == started_event.user_id
    assert scraper_operation.organization_id == started_event.organization_id


def test_should_contain_correct_fail_reasion(
    scraper_state_changed_event_factory,
    scraping_operation_history_factory,
):
    scraper_operation = scraping_operation_history_factory(operation_id="123")
    fail_reason = "Some reason"
    failed_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.FAILED, operation_id="123", reason=fail_reason
    )

    scraper_operation.apply(failed_event)

    assert scraper_operation.fail_reason == fail_reason
    assert scraper_operation.state == ScraperBinaryStatus.FAILED
    assert scraper_operation.operation_id == failed_event.operation_id
    assert scraper_operation.user_id == failed_event.user_id
    assert scraper_operation.organization_id == failed_event.organization_id
