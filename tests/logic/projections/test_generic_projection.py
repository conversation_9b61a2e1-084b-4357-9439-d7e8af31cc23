from unittest.mock import AsyncMock

import pytest
from pydantic import BaseModel

from scraper_service.logic.projections.generic_projection import GenericProjection


class MockEvent(BaseModel):
    organization_id: str = "org1"
    user_id: str = "user1"
    source: str = "test_source"


class MockEntity(BaseModel):
    id: str
    applied_events: list[MockEvent] = []

    def apply(self, event):
        self.applied_events.append(event)


async def test_generic_projection_basic_flow(anyio_backend):
    mock_repo = AsyncMock()
    mock_repo.get.return_value = MockEntity(id="test", applied_events=[])

    async def get_entity(repo, event):
        return await repo.get(event.organization_id)

    projection = GenericProjection(
        repository=mock_repo,
        event_types=[MockEvent],
        get_entity=get_entity,
    )

    event = MockEvent(organization_id="org1")
    await projection.handle(event)

    mock_repo.get.assert_called_once_with("org1")
    mock_repo.save.assert_called_once()

    saved_entity = mock_repo.save.call_args[0][0]
    assert event in saved_entity.applied_events


async def test_generic_projection_standard_pattern(anyio_backend):
    mock_repo = AsyncMock()
    mock_repo.get.return_value = MockEntity(id="test", applied_events=[])

    projection = GenericProjection(
        repository=mock_repo,
        event_types=[MockEvent],
        get_entity=lambda repo, event: repo.get(
            event.organization_id, event.user_id, event.source
        ),
    )

    event = MockEvent(organization_id="org1", user_id="user1", source="source1")
    await projection.handle(event)

    mock_repo.get.assert_called_once_with("org1", "user1", "source1")
    mock_repo.save.assert_called_once()


async def test_generic_projection_event_based_pattern(anyio_backend):
    mock_repo = AsyncMock()
    mock_repo.get.return_value = MockEntity(id="test", applied_events=[])

    projection = GenericProjection(
        repository=mock_repo,
        event_types=[MockEvent],
        get_entity=lambda repo, event: repo.get(event),
    )

    event = MockEvent()
    await projection.handle(event)

    mock_repo.get.assert_called_once_with(event)
    mock_repo.save.assert_called_once()


class EventA(BaseModel):
    id: str = "a"


class EventB(BaseModel):
    id: str = "b"


async def test_generic_projection_multiple_event_types(anyio_backend):
    mock_repo = AsyncMock()
    mock_repo.get.return_value = MockEntity(id="test", applied_events=[])

    projection = GenericProjection(
        repository=mock_repo,
        event_types=[EventA, EventB],
        get_entity=lambda repo, event: repo.get(event.id),
    )

    await projection.handle(EventA())
    await projection.handle(EventB())

    assert mock_repo.get.call_count == 2
    assert mock_repo.save.call_count == 2

    calls = mock_repo.get.call_args_list
    assert calls[0][0][0] == "a"
    assert calls[1][0][0] == "b"


class RegisteredEvent(BaseModel):
    pass


class UnregisteredEvent(BaseModel):
    pass


async def test_generic_projection_unregistered_event_raises_error(anyio_backend):
    mock_repo = AsyncMock()
    mock_repo.get.return_value = MockEntity(id="test", applied_events=[])

    projection = GenericProjection(
        repository=mock_repo,
        event_types=[RegisteredEvent],
        get_entity=lambda repo, event: repo.get("test"),
    )

    with pytest.raises(
        NotImplementedError, match="No handler registered for event type"
    ):
        await projection.handle(UnregisteredEvent())
