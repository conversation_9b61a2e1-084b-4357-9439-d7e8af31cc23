import pytest

from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.models.scraper_status import Scraper<PERSON>tatus


@pytest.mark.parametrize(
    ("input_state", "expected_state"),
    [
        (ScraperBinaryStatus.STARTED, ScraperBinaryStatus.STARTED),
        (ScraperBinaryStatus.FINISHED, ScraperBinaryStatus.FINISHED),
        (ScraperBinaryStatus.FAILED, ScraperBinaryStatus.FAILED),
        (ScraperBinaryStatus.STOPPED, ScraperBinaryStatus.STOPPED),
        (ScraperBinaryStatus.DISABLED, ScraperBinaryStatus.DISABLED),
    ],
)
def test_should_have_state_of_last_event(
    input_state,
    expected_state,
    test_organization_id,
    test_user_id,
    scraper_state_changed_event_factory,
):
    # Given
    status = ScraperStatus(
        organization_id=test_organization_id, user_id=test_user_id, source="steam_sales"
    )
    event = scraper_state_changed_event_factory(state=input_state)

    # When
    status.apply(event)

    # Then
    assert status.state == expected_state


def test_should_increase_fail_counter_on_consecutive_failed_events(
    test_organization_id,
    test_user_id,
    scraper_state_changed_event_factory,
):
    status = ScraperStatus(
        organization_id=test_organization_id, user_id=test_user_id, source="steam_sales"
    )

    for _ in range(3):
        status.apply(
            scraper_state_changed_event_factory(state=ScraperBinaryStatus.FAILED)
        )

    assert status.consecutive_failed_scrape_count == 3
    assert status.state == ScraperBinaryStatus.FAILED


def test_should_reset_fail_counter_on_success(
    test_organization_id,
    test_user_id,
    scraper_state_changed_event_factory,
):
    status = ScraperStatus(
        organization_id=test_organization_id, user_id=test_user_id, source="steam_sales"
    )

    for _ in range(3):
        status.apply(
            scraper_state_changed_event_factory(state=ScraperBinaryStatus.FAILED)
        )

    status.apply(
        scraper_state_changed_event_factory(state=ScraperBinaryStatus.FINISHED)
    )

    assert status.consecutive_failed_scrape_count == 0
    assert status.state == ScraperBinaryStatus.FINISHED


def test_should_contain_previous_data_after_a_stop_event(
    test_organization_id, test_user_id, scraper_state_changed_event_factory
):
    status = ScraperStatus(
        organization_id=test_organization_id, user_id=test_user_id, source="steam_sales"
    )
    status.apply(scraper_state_changed_event_factory(state=ScraperBinaryStatus.FAILED))

    status.apply(scraper_state_changed_event_factory(state=ScraperBinaryStatus.STOPPED))

    assert status.consecutive_failed_scrape_count == 1
    assert status.state == ScraperBinaryStatus.STOPPED


def test_should_display_proper_last_success_date(
    test_organization_id, test_user_id, scraper_state_changed_event_factory
):
    status = ScraperStatus(
        organization_id=test_organization_id, user_id=test_user_id, source="steam_sales"
    )

    finished_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.FINISHED, received_at="2021-02-01T00:00:00Z"
    )
    failed_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.FAILED, received_at="2022-02-01T00:00:00Z"
    )
    stopped_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STOPPED, received_at="2023-02-01T00:00:00Z"
    )

    status.apply(finished_event)
    status.apply(failed_event)
    status.apply(stopped_event)

    assert status.last_fail_timestamp == failed_event.received_at
    assert status.last_success_timestamp == finished_event.received_at
    assert status.updated_at == stopped_event.received_at
    assert status.last_operation_id == stopped_event.operation_id


def test_should_display_proper_last_fail_date(
    test_organization_id, test_user_id, scraper_state_changed_event_factory
):
    status = ScraperStatus(
        organization_id=test_organization_id, user_id=test_user_id, source="steam_sales"
    )

    failed_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.FAILED, received_at="2022-02-01T00:00:00Z"
    )
    finished_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.FINISHED, received_at="2021-02-01T00:00:00Z"
    )

    status.apply(failed_event)
    status.apply(finished_event)

    assert status.last_operation_id == finished_event.operation_id
    assert status.last_fail_timestamp == failed_event.received_at
    assert status.last_success_timestamp == finished_event.received_at


def test_should_display_proper_fail_reason(
    test_organization_id, test_user_id, scraper_state_changed_event_factory
):
    status = ScraperStatus(
        organization_id=test_organization_id, user_id=test_user_id, source="steam_sales"
    )

    failed_event = scraper_state_changed_event_factory(state=ScraperBinaryStatus.FAILED)
    failed_event.body.reason = "UNEXPECTED_ERROR"

    status.apply(failed_event)

    assert status.last_fail_reason == "UNEXPECTED_ERROR"


def test_operation_id_was_changed(
    test_organization_id, test_user_id, scraper_state_changed_event_factory
):
    status = ScraperStatus(
        organization_id=test_organization_id, user_id=test_user_id, source="steam_sales"
    )

    failed_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.FAILED,
        received_at="2022-02-01T00:00:00Z",
        operation_id="first",
    )
    status.apply(failed_event)

    assert status.last_operation_id == failed_event.operation_id

    started_event = scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.STARTED,
        received_at="2021-02-01T00:00:00Z",
        operation_id="second",
    )
    status.apply(started_event)

    assert status.last_operation_id == started_event.operation_id
