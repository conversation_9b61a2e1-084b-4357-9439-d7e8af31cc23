"""Tests for UserFeatureFlagsRepository."""

import pytest
from sqlalchemy import delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from scraper_service.logic.entities import UserFeatureFlags
from scraper_service.logic.repositories.user_feature_flags import (
    UserFeatureFlagsRepository,
    _UserFeatureFlagsModel,
)


@pytest.mark.asyncio
async def test_bulk_override_with_entities(async_session: AsyncSession):
    """Test bulk_override method with a list of entities."""
    # Arrange
    repo = UserFeatureFlagsRepository(async_session)
    
    # Create some initial data in the database
    initial_entities = [
        _UserFeatureFlagsModel(user_id="old_user1", feature_flags=["old_flag1"]),
        _UserFeatureFlagsModel(user_id="old_user2", feature_flags=["old_flag2"]),
    ]
    async_session.add_all(initial_entities)
    await async_session.flush()
    
    # Verify initial data exists
    stmt = select(_UserFeatureFlagsModel)
    result = await async_session.execute(stmt)
    initial_count = len(result.scalars().all())
    assert initial_count == 2
    
    # New entities to replace all existing ones
    new_entities = [
        UserFeatureFlags(id="user1", feature_flags=["flag1", "flag2"]),
        UserFeatureFlags(id="user2", feature_flags=["flag3"]),
        UserFeatureFlags(id="user3", feature_flags=["flag1", "flag4", "flag5"]),
    ]
    
    # Act
    await repo.bulk_override(new_entities)
    await async_session.flush()
    
    # Assert
    # Check that all old data is gone and new data is present
    stmt = select(_UserFeatureFlagsModel)
    result = await async_session.execute(stmt)
    all_entities = result.scalars().all()
    
    assert len(all_entities) == 3
    
    # Verify the new entities are correctly stored
    user_ids = {entity.user_id for entity in all_entities}
    assert user_ids == {"user1", "user2", "user3"}
    
    # Verify specific entity data
    for entity in all_entities:
        if entity.user_id == "user1":
            assert entity.feature_flags == ["flag1", "flag2"]
        elif entity.user_id == "user2":
            assert entity.feature_flags == ["flag3"]
        elif entity.user_id == "user3":
            assert entity.feature_flags == ["flag1", "flag4", "flag5"]


@pytest.mark.asyncio
async def test_bulk_override_with_empty_list(async_session: AsyncSession):
    """Test bulk_override method with an empty list (should clear all data)."""
    # Arrange
    repo = UserFeatureFlagsRepository(async_session)
    
    # Create some initial data in the database
    initial_entities = [
        _UserFeatureFlagsModel(user_id="user1", feature_flags=["flag1"]),
        _UserFeatureFlagsModel(user_id="user2", feature_flags=["flag2"]),
    ]
    async_session.add_all(initial_entities)
    await async_session.flush()
    
    # Verify initial data exists
    stmt = select(_UserFeatureFlagsModel)
    result = await async_session.execute(stmt)
    initial_count = len(result.scalars().all())
    assert initial_count == 2
    
    # Act
    await repo.bulk_override([])
    await async_session.flush()
    
    # Assert
    # Check that all data is gone
    stmt = select(_UserFeatureFlagsModel)
    result = await async_session.execute(stmt)
    all_entities = result.scalars().all()
    
    assert len(all_entities) == 0


@pytest.mark.asyncio
async def test_bulk_override_on_empty_table(async_session: AsyncSession):
    """Test bulk_override method when table is initially empty."""
    # Arrange
    repo = UserFeatureFlagsRepository(async_session)
    
    # Ensure table is empty
    delete_stmt = delete(_UserFeatureFlagsModel)
    await async_session.execute(delete_stmt)
    await async_session.flush()
    
    new_entities = [
        UserFeatureFlags(id="user1", feature_flags=["flag1"]),
        UserFeatureFlags(id="user2", feature_flags=["flag2", "flag3"]),
    ]
    
    # Act
    await repo.bulk_override(new_entities)
    await async_session.flush()
    
    # Assert
    stmt = select(_UserFeatureFlagsModel)
    result = await async_session.execute(stmt)
    all_entities = result.scalars().all()
    
    assert len(all_entities) == 2
    
    user_ids = {entity.user_id for entity in all_entities}
    assert user_ids == {"user1", "user2"}


@pytest.mark.asyncio
async def test_save_and_get_methods(async_session: AsyncSession):
    """Test the save and get methods work correctly."""
    # Arrange
    repo = UserFeatureFlagsRepository(async_session)
    entity = UserFeatureFlags(id="test_user", feature_flags=["flag1", "flag2"])
    
    # Act - Save
    await repo.save(entity)
    await async_session.flush()
    
    # Act - Get
    retrieved_entity = await repo.get("test_user")
    
    # Assert
    assert retrieved_entity.id == "test_user"
    assert retrieved_entity.feature_flags == ["flag1", "flag2"]


@pytest.mark.asyncio
async def test_get_nonexistent_user(async_session: AsyncSession):
    """Test get method returns empty flags for nonexistent user."""
    # Arrange
    repo = UserFeatureFlagsRepository(async_session)
    
    # Act
    retrieved_entity = await repo.get("nonexistent_user")
    
    # Assert
    assert retrieved_entity.id == "nonexistent_user"
    assert retrieved_entity.feature_flags == []
