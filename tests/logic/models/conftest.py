from datetime import datetime

import pytest

from scraper_service.logic.entities import DateRange, ScraperBinaryStatus
from scraper_service.logic.events.report_uploaded import (
    ReportInfo,
    ReportUploadedEvent,
    ReportUploadEventBody,
    ScrapeResult,
)
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedEvent


@pytest.fixture
def date_from():
    return "2025-01-01"


@pytest.fixture
def date_to():
    return "2025-01-08"


@pytest.fixture
def date_range(date_from, date_to):
    days = (datetime.fromisoformat(date_to) - datetime.fromisoformat(date_from)).days
    return DateRange(date_from=date_from, date_to=date_to, days_in_range=days)


@pytest.fixture
def scheduled_scraper_state_changed_event(
    scraper_state_changed_event_factory, date_range
):
    return scraper_state_changed_event_factory(
        state=ScraperBinaryStatus.SCHEDULED,
        date_ranges=[date_range],
    )


@pytest.fixture
def started_scraper_state_changed_event(date_from, date_to):
    return ScraperStateChangedEvent(
        client_timestamp=datetime.now(),
        user_id="123",
        organization_id="123",
        origin="123",
        operation_id="123",
    )


@pytest.fixture
def report_uploaded_event(date_from, date_to):
    return ReportUploadedEvent(
        client_timestamp=datetime.now(),
        user_id="123",
        organization_id="123",
        origin="123",
        operation_id="123",
        body=ReportUploadEventBody(
            source="steam_sales",
            report_info=ReportInfo(
                id=1,
                studio_id=1,
                original_name="test",
                upload_date=datetime.now().isoformat(),
                file_path_raw="test",
                date_from=date_from,
                date_to=date_to,
                source="steam_sales",
                no_data=False,
                state="test",
            ),
            scrape_result=ScrapeResult(
                report_file_name="test",
                start_date=date_from,
                end_date=date_to,
                source="steam_sales",
                no_data=False,
            ),
        ),
    )


@pytest.fixture
def report_uploaded_event_factory(report_uploaded_event):
    def factory(report_id: int):
        event_copy = report_uploaded_event.model_copy(deep=True)
        event_copy.body.report_info.id = report_id
        return event_copy

    return factory
