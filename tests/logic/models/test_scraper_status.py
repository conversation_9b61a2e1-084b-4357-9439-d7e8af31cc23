import pytest

from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.models.scraper_status import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ScraperStatus


def test_should_get_unconfigured_status_for_new_status_without_events_applied():
    status = ScraperStatus(organization_id="123", user_id="123", source="steam_sales")
    assert status.state == ScraperBinaryStatus.UNCONFIGURED


def test_should_apply_scheduled_scraper_state_changed_event(
    scheduled_scraper_state_changed_event, date_from, date_to
) -> None:
    status = ScraperStatus(organization_id="123", user_id="123", source="steam_sales")
    event = scheduled_scraper_state_changed_event

    status.apply(event)

    assert status.model_dump() == {
        "consecutive_failed_scrape_count": 0,
        "last_command": ScraperCommand.SCRAPE,
        "last_fail_reason": None,
        "last_fail_timestamp": None,
        "last_operation_id": "12390019239abc",
        "last_origin": "fake-1.0.0",
        "last_report_ids": [],
        "organization_id": "123",
        "user_id": "123",
        "source": "steam_sales",
        "state": ScraperBinaryStatus.SCHEDULED,
        "created_at": status.created_at,
        "updated_at": status.updated_at,
        "last_scrape_date_ranges": [
            {
                "date_from": date_from,
                "date_to": date_to,
                "days_in_range": 7,
            }
        ],
        "last_success_timestamp": None,
        "triggered_by": None,
        "uses_manual_session": None,
        "version": 0,
    }


@pytest.mark.parametrize(
    ("input_state", "expected"),
    [
        (ScraperBinaryStatus.STARTED, ScraperBinaryStatus.STARTED),
        (ScraperBinaryStatus.FINISHED, ScraperBinaryStatus.FINISHED),
        (ScraperBinaryStatus.FAILED, ScraperBinaryStatus.FAILED),
        (ScraperBinaryStatus.STOPPED, ScraperBinaryStatus.STOPPED),
    ],
)
def test_should_set_status_on_single_event(
    scraper_state_changed_event_factory,
    input_state,
    expected,
):
    status = ScraperStatus(organization_id="123", user_id="123", source="steam_sales")
    event = scraper_state_changed_event_factory(state=input_state)
    status.apply(event)
    assert status.state == expected


@pytest.mark.parametrize(
    ("previous_state", "input_state", "expected"),
    [
        (
            ScraperBinaryStatus.FINISHED,
            ScraperBinaryStatus.STARTED,
            ScraperBinaryStatus.STARTED,
        ),
        (
            ScraperBinaryStatus.FAILED,
            ScraperBinaryStatus.FINISHED,
            ScraperBinaryStatus.FINISHED,
        ),
        (
            ScraperBinaryStatus.FINISHED,
            ScraperBinaryStatus.FAILED,
            ScraperBinaryStatus.FAILED,
        ),
        (
            ScraperBinaryStatus.FINISHED,
            ScraperBinaryStatus.STOPPED,
            ScraperBinaryStatus.STOPPED,
        ),
        (
            ScraperBinaryStatus.FINISHED,
            ScraperBinaryStatus.DISABLED,
            ScraperBinaryStatus.DISABLED,
        ),
        (
            ScraperBinaryStatus.DISABLED,
            ScraperBinaryStatus.FINISHED,
            ScraperBinaryStatus.FINISHED,
        ),
    ],
)
def test_should_update_status_on_event(
    scraper_state_changed_event_factory,
    previous_state,
    input_state,
    expected,
):
    status = ScraperStatus(organization_id="123", user_id="123", source="steam_sales")
    event1 = scraper_state_changed_event_factory(state=previous_state)
    event2 = scraper_state_changed_event_factory(state=input_state)

    status.apply(event1)
    status.apply(event2)

    assert status.state == expected


def test_should_apply_report_uploaded_event(
    report_uploaded_event_factory, date_from, date_to
) -> None:
    status = ScraperStatus(organization_id="123", user_id="123", source="steam_sales")
    event1 = report_uploaded_event_factory(42)
    event2 = report_uploaded_event_factory(43)

    expected_status = status.model_copy(deep=True)

    status.apply(event1)
    assert status.last_report_ids == [event1.body.report_info.id]
    assert status.last_origin == event1.origin
    assert status.last_operation_id == event1.operation_id
    assert status.updated_at == event1.received_at

    status.apply(event2)
    assert status.last_report_ids == [
        event1.body.report_info.id,
        event2.body.report_info.id,
    ]
    assert status.last_origin == event2.origin
    assert status.last_operation_id == event2.operation_id
    assert status.updated_at == event2.received_at

    expected_to_be_updated = {
        "last_report_ids",
        "last_origin",
        "last_operation_id",
        "updated_at",
    }
    # the rest of the fields should be the same
    assert status.model_dump(
        exclude=expected_to_be_updated
    ) == expected_status.model_dump(exclude=expected_to_be_updated)


def test_if_scraper_status_is_source_agnostic_and_accept_unknown_source_values():
    status = ScraperStatus(
        organization_id="123", user_id="123", source="some_new_source"
    )

    assert status.source == "some_new_source"
    assert status.state == ScraperBinaryStatus.UNCONFIGURED
