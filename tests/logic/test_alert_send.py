from contextlib import nullcontext
from unittest.mock import AsyncMock, patch

import pytest

from scraper_service.logic.alert_sender import <PERSON>ert<PERSON><PERSON>
from scraper_service.logic.entities import ScraperBinaryStatus


@pytest.fixture
def data_office_markdown_link():
    org_id = "o-AL6Tv7"
    return f"[DataOffice](https://dataoffice.indiebi.dev/scraper_statuses/?tableState=%7B%22searchBuilder%22%3A%7B%22criteria%22%3A%5B%7B%22condition%22%3A%22%3D%22%2C%22data%22%3A%22Org%20ID%22%2C%22origData%22%3A%22organization_id%22%2C%22type%22%3A%22string%22%2C%22value%22%3A%5B%22{org_id}%22%5D%7D%5D%2C%22logic%22%3A%22AND%22%7D%7D)"


@pytest.fixture
def alert_sender(anyio_backend, test_config) -> AlertSender:
    # we pass nullcontext as sessionmaker because we don't use db in this tests. Instead
    # all data is mocked and in case if we miss some mock we will get an error
    return AlertSender(test_config, sessionmaker=nullcontext)


async def test_run_checks_sends_message(
    alert_sender: AlertSender, scraper_status_factory, data_office_markdown_link
):
    sample_projection_1 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2025-01-13 18:00:00.000000",
        state=ScraperBinaryStatus.FAILED,
    )
    sample_projection_2 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2024-01-13 18:00:00.000000",
        source="microsoft_sales",
        state=ScraperBinaryStatus.FAILED,
    )

    alert_sender.previous_statuses = {
        ("o-AL6Tv7", "microsoft_sales"),
        ("o-AL6Tv7", "nintendo_sales"),
    }
    alert_sender.previous_sources = {"nintendo_sales", "nintendo_discounts"}
    with (
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_statuses_needing_attention",
            new_callable=AsyncMock,
            return_value=[sample_projection_1, sample_projection_2],
        ),
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_alerting_sources",
            new_callable=AsyncMock,
            return_value=[("microsoft_sales", 0.5), ("nintendo_sales", 0.8)],
        ),
        patch(
            "scraper_service.logic.alert_sender.send_to_channel",
            new_callable=AsyncMock,
        ) as mock_send_to_channel,
    ):
        await alert_sender.run_checks()
        # Should be called twice - once for statuses and once for global issues
        assert mock_send_to_channel.call_count == 2

        # Check status message format
        status_call = mock_send_to_channel.call_args_list[0]
        expected_status_msg = (
            "| Organization | Failed Sources | Last Updated | Link |\n"
            "|--------------|----------------|--------------|------|\n"
            f"| o-AL6Tv7 | microsoft_sales, 🔺 steam_sales, ✅ nintendo_sales | 2025-01-13 18:00:00 | {data_office_markdown_link} |\n\n"
            "🔺- new\n"
            "✅ - resolved"
        )
        assert status_call[0][1].startswith(expected_status_msg)

        # Check global issues message format
        global_call = mock_send_to_channel.call_args_list[1]
        expected_global_msg = (
            "### Global platform issues:\n\n"
            "| source | affected |\n"
            "|--------|----------|\n"
            "| 🔺 microsoft_sales | 50 % affected |\n"
            "| nintendo_sales | 80 % affected |\n"
            "| nintendo_discounts | ✅ Resolved |\n\n"
            "🔺- new\n"
            "✅ - resolved"
        )
        assert global_call[0][1] == expected_global_msg

        # Check that sets are updated correctly
        assert alert_sender.previous_statuses == {
            ("o-AL6Tv7", "microsoft_sales"),
            ("o-AL6Tv7", "steam_sales"),
        }
        assert alert_sender.previous_sources == {"nintendo_sales", "microsoft_sales"}


async def test_run_checks_does_not_send_message_if_no_statuses(
    alert_sender: AlertSender,
):
    with (
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_statuses_needing_attention",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_alerting_sources",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "scraper_service.logic.alert_sender.send_to_channel",
            new_callable=AsyncMock,
        ) as mock_send_to_channel,
    ):
        await alert_sender.run_checks()
        mock_send_to_channel.assert_not_called()
        assert alert_sender.previous_statuses == set()
        assert alert_sender.previous_sources == set()


async def test_run_checks_sends_only_sources_statuses(
    alert_sender: AlertSender, test_config
):
    with (
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_statuses_needing_attention",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_alerting_sources",
            new_callable=AsyncMock,
            return_value=[("source1", 0.5)],
        ),
        patch(
            "scraper_service.logic.alert_sender.send_to_channel",
            new_callable=AsyncMock,
        ) as mock_send_to_channel,
    ):
        await alert_sender.run_checks()
        mock_send_to_channel.assert_called_once()
        expected_msg = (
            "### Global platform issues:\n\n"
            "| source | affected |\n"
            "|--------|----------|\n"
            "| 🔺 source1 | 50 % affected |\n\n"
            "🔺- new\n"
            "✅ - resolved"
        )
        mock_send_to_channel.assert_called_with(
            test_config.teams_webhook_url, expected_msg
        )
        assert alert_sender.previous_sources == {"source1"}
        assert alert_sender.previous_statuses == set()


async def test_run_checks_starts_party_when_all_are_resolved(
    alert_sender: AlertSender, test_config
):
    alert_sender.previous_sources = {"nintendo_sales"}
    with (
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_statuses_needing_attention",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_alerting_sources",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "scraper_service.logic.alert_sender.send_to_channel",
            new_callable=AsyncMock,
        ) as mock_send_to_channel,
    ):
        await alert_sender.run_checks()
        mock_send_to_channel.assert_called_once()
        expected_msg = "🎉🪄🎉 ALL ISSUES RESOLVED 🎉🪄🎉"
        mock_send_to_channel.assert_called_with(
            test_config.teams_webhook_url, expected_msg
        )
        assert alert_sender.previous_sources == set()
        assert alert_sender.previous_statuses == set()


async def test_run_checks_sends_only_scraper_statuses(
    alert_sender: AlertSender,
    test_config,
    scraper_status_factory,
    data_office_markdown_link,
):
    sample_projection = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2025-01-13 18:00:00.000000",
        state=ScraperBinaryStatus.FAILED,
    )

    with (
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_statuses_needing_attention",
            new_callable=AsyncMock,
            return_value=[sample_projection],
        ),
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_alerting_sources",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "scraper_service.logic.alert_sender.send_to_channel",
            new_callable=AsyncMock,
        ) as mock_send_to_channel,
    ):
        await alert_sender.run_checks()
        mock_send_to_channel.assert_called_once()
        expected_msg = (
            "| Organization | Failed Sources | Last Updated | Link |\n"
            "|--------------|----------------|--------------|------|\n"
            f"| o-AL6Tv7 | 🔺 steam_sales | 2025-01-13 18:00:00 | {data_office_markdown_link} |\n\n"
            "🔺- new\n"
            "✅ - resolved"
        )
        mock_send_to_channel.assert_called_with(
            test_config.teams_webhook_url, expected_msg
        )
        assert alert_sender.previous_statuses == {("o-AL6Tv7", "steam_sales")}
        assert alert_sender.previous_sources == set()


async def test_run_checks_sends_message_only_once(
    alert_sender: AlertSender, test_config
):
    with (
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_statuses_needing_attention",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_alerting_sources",
            new_callable=AsyncMock,
            return_value=[("source1", 0.5)],
        ),
        patch(
            "scraper_service.logic.alert_sender.send_to_channel",
            new_callable=AsyncMock,
        ) as mock_send_to_channel,
    ):
        # First run
        await alert_sender.run_checks()
        mock_send_to_channel.assert_called_once()
        expected_msg = (
            "### Global platform issues:\n\n"
            "| source | affected |\n"
            "|--------|----------|\n"
            "| 🔺 source1 | 50 % affected |\n\n"
            "🔺- new\n"
            "✅ - resolved"
        )
        mock_send_to_channel.assert_called_with(
            test_config.teams_webhook_url, expected_msg
        )
        assert alert_sender.previous_sources == {"source1"}

        # Second run with same data
        mock_send_to_channel.reset_mock()

        await alert_sender.run_checks()
        # send_to_channel should not be called again since sources haven't changed
        mock_send_to_channel.assert_not_called()
        assert alert_sender.previous_sources == {"source1"}


async def test_run_checks_sends_message_after_reset(
    alert_sender: AlertSender, test_config
):
    with (
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_statuses_needing_attention",
            new_callable=AsyncMock,
            return_value=[],
        ),
        patch(
            "scraper_service.logic.repositories.scraper_status.ScraperStatusRepository.get_alerting_sources",
            new_callable=AsyncMock,
            return_value=[("source1", 0.5)],
        ),
        patch(
            "scraper_service.logic.alert_sender.send_to_channel",
            new_callable=AsyncMock,
        ) as mock_send_to_channel,
    ):
        # First run
        await alert_sender.run_checks()
        mock_send_to_channel.assert_called_once()
        expected_msg = (
            "### Global platform issues:\n\n"
            "| source | affected |\n"
            "|--------|----------|\n"
            "| 🔺 source1 | 50 % affected |\n\n"
            "🔺- new\n"
            "✅ - resolved"
        )
        mock_send_to_channel.assert_called_with(
            test_config.teams_webhook_url, expected_msg
        )
        assert alert_sender.previous_sources == {"source1"}

        # Reset and run again
        mock_send_to_channel.reset_mock()
        alert_sender.reset_last_sent_message()
        assert alert_sender.previous_sources == set()
        assert alert_sender.previous_statuses == set()

        # After reset, should send message again
        await alert_sender.run_checks()
        mock_send_to_channel.assert_called_once()
        mock_send_to_channel.assert_called_with(
            test_config.teams_webhook_url, expected_msg
        )
        assert alert_sender.previous_sources == {"source1"}


def test_generate_dataoffice_link(alert_sender):
    org_id = "o-NkhYdc"
    data_office_link = alert_sender.generate_dataoffice_status_link_for_org(org_id)
    assert (
        data_office_link
        == f"https://dataoffice.indiebi.dev/scraper_statuses/?tableState=%7B%22searchBuilder%22%3A%7B%22criteria%22%3A%5B%7B%22condition%22%3A%22%3D%22%2C%22data%22%3A%22Org%20ID%22%2C%22origData%22%3A%22organization_id%22%2C%22type%22%3A%22string%22%2C%22value%22%3A%5B%22{org_id}%22%5D%7D%5D%2C%22logic%22%3A%22AND%22%7D%7D"
    )
