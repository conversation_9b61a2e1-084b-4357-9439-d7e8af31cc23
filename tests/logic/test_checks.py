import pytest
from freezegun import freeze_time

from scraper_service.logic.entities import ScraperBinaryStatus


@pytest.mark.skip(reason="require jwt fixture for many users/organizations")
async def test_correctly_finds_alerting_sources(
    async_jwt_client, scraper_state_changed_event_factory, scraper_status_repository
):
    list_of_states = [
        {
            "organization_id": "org1",
            "user_id": "user1",
            "source": "microsoft_sales",
            "state": ScraperBinaryStatus.FAILED,
        },
        {
            "organization_id": "org2",
            "user_id": "user2",
            "source": "microsoft_sales",
            "state": ScraperBinaryStatus.FINISHED,
        },
        {
            "organization_id": "org2",
            "user_id": "user2",
            "source": "steam_sales",
            "state": ScraperBinaryStatus.FINISHED,
        },
    ]
    for state in list_of_states:
        event = scraper_state_changed_event_factory(
            organization_id=state["organization_id"],
            user_id=state["user_id"],
            source=state["source"],
            state=state["state"],
        )
        await async_jwt_client.post(
            "/external/scraper_events", content=event.model_dump_json()
        )
    repo = scraper_status_repository
    alerting_sources = await repo.get_alerting_sources(0.5)
    assert len(alerting_sources) == 0
    alerting_sources = await repo.get_alerting_sources(0.3)
    assert len(alerting_sources) == 1
    assert alerting_sources[0][0] == "microsoft_sales"


@pytest.mark.skip(reason="require jwt fixture for many users/organizations")
async def test_ignores_sources(
    async_jwt_client, scraper_state_changed_event_factory, scraper_status_repository
):
    list_of_states = [
        {
            "organization_id": "org1",
            "user_id": "user1",
            "source": "microsoft_sales",
            "state": ScraperBinaryStatus.FAILED,
        },
        {
            "organization_id": "org2",
            "user_id": "user2",
            "source": "microsoft_sales",
            "state": ScraperBinaryStatus.FINISHED,
        },
        {
            "organization_id": "org2",
            "user_id": "user2",
            "source": "epic_sales",
            "state": ScraperBinaryStatus.FAILED,
        },
    ]
    for state in list_of_states:
        event = scraper_state_changed_event_factory(
            organization_id=state["organization_id"],
            user_id=state["user_id"],
            source=state["source"],
            state=state["state"],
        )
        await async_jwt_client.post(
            "/external/scraper_events", content=event.model_dump_json()
        )
    repo = scraper_status_repository
    alerting_sources = await repo.get_alerting_sources(
        0.3, ignored_sources=["epic_sales"]
    )
    assert len(alerting_sources) == 1
    assert alerting_sources[0][0] == "microsoft_sales"


async def test_correctly_finds_alerting_sources_with_all_blocked(
    async_api_key_client, scraper_state_changed_event_factory, scraper_status_repository
):
    list_of_events = [
        scraper_state_changed_event_factory(
            organization_id="org1",
            source="microsoft_sales",
            state=ScraperBinaryStatus.MANUALLY_BLOCKED,
        ),
        scraper_state_changed_event_factory(
            organization_id="org2",
            source="microsoft_sales",
            state=ScraperBinaryStatus.MANUALLY_BLOCKED,
        ),
        scraper_state_changed_event_factory(
            organization_id="org2",
            source="steam_sales",
            state=ScraperBinaryStatus.MANUALLY_BLOCKED,
        ),
    ]
    for event in list_of_events:
        await async_api_key_client.post(
            "/external/scraper_events", content=event.model_dump_json()
        )
    repo = scraper_status_repository
    alerting_sources = await repo.get_alerting_sources(0.1)
    assert len(alerting_sources) == 0


async def test_correctly_detects_long_running_scrapes(
    async_jwt_client, scraper_state_changed_event_factory, scraper_status_repository
):
    event = scraper_state_changed_event_factory(
        organization_id="org2", source="steam_sales", state=ScraperBinaryStatus.STARTED
    )
    await async_jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )
    with freeze_time("3000-01-01 12:00:00"):
        repo = scraper_status_repository
        long_running_scrapers = await repo.get_long_running_scrapes()
        assert len(long_running_scrapers) == 1


async def test_do_not_detect_long_running_scrapes_if_not_started(
    async_api_key_client, scraper_state_changed_event_factory, scraper_status_repository
):
    with freeze_time("2021-01-01 12:00:00"):
        event = scraper_state_changed_event_factory(
            organization_id="org1",
            source="steam_sales",
            state=ScraperBinaryStatus.FAILED,
        )
        await async_api_key_client.post(
            "/external/scraper_events", content=event.model_dump_json()
        )
    repo = scraper_status_repository
    long_running_scrapers = await repo.get_long_running_scrapes()
    assert len(long_running_scrapers) == 0


async def test_correctly_finds_failed_scrapes(
    async_jwt_client, scraper_state_changed_event_factory, scraper_status_repository
):
    event = scraper_state_changed_event_factory(
        organization_id="org1", source="steam_sales", state=ScraperBinaryStatus.FAILED
    )
    await async_jwt_client.post(
        "/external/scraper_events", content=event.model_dump_json()
    )
    repo = scraper_status_repository
    failed_scrapes = await repo.get_failed_scrape_statuses()
    assert len(failed_scrapes) == 1
    assert failed_scrapes[0].source == "steam_sales"
    assert failed_scrapes[0].state == ScraperBinaryStatus.FAILED
