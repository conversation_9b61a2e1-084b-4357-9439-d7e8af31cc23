import pytest

from scraper_service.config import Config
from scraper_service.logic.entities import DatabaseURL


def test_config_database_updates_psycopg2_to_psycopg():
    config = Config(database_url="postgresql+psycopg2://user:pass@localhost/dbname")
    assert config.database_url == DatabaseURL(
        "postgresql+psycopg://user:pass@localhost/dbname"
    )


def test_config_database_updates_postgresql_to_psycopg():
    config = Config(database_url="postgresql://user:pass@localhost/dbname")
    assert config.database_url == DatabaseURL(
        "postgresql+psycopg://user:pass@localhost/dbname"
    )


def test_config__database_psycopg_no_change():
    config = Config(database_url="postgresql+psycopg://user:pass@localhost/dbname")
    assert config.database_url == DatabaseURL(
        "postgresql+psycopg://user:pass@localhost/dbname"
    )


def test_config_unsupported_database():
    with pytest.raises(ValueError, match="Database URL must be a PostgreSQL URL"):
        Config(database_url="mysql://user:pass@localhost/dbname")
