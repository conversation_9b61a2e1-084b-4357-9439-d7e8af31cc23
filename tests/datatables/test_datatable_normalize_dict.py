import pytest

from scraper_service.datatables.datatable import DataTable


@pytest.mark.parametrize(
    ("input_data", "expected_output"),
    [
        ({0: "value1", 1: "value2"}, ["value1", "value2"]),
        (
            {0: {0: "value1", 1: "value2"}, 1: {0: "value3"}},
            [["value1", "value2"], ["value3"]],
        ),
        ({"": "google_sales"}, ["google_sales"]),
        (
            [{"key1": "value1"}, {"key2": "value2"}],
            [{"key1": "value1"}, {"key2": "value2"}],
        ),
        ("string_value", "string_value"),
    ],
)
def test_normalize_dict_to_list(input_data, expected_output):
    assert DataTable._normalize_dict_to_list(input_data) == expected_output
