from typing import Any

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from scraper_service.datatables import DataTable
from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.repositories.scraper_status import _ScraperStatusModel


@pytest.fixture
def scraper_status_data_table(async_session: AsyncSession):
    return DataTable(session=async_session, table=_ScraperStatusModel)


async def test_datatable_pagination(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
    test_organization_id,
) -> None:
    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, length=2
    )

    for i in range(6):
        sample_projection = scraper_status_factory(
            created_at="2025-01-13 18:00:00.000000",
            updated_at="2025-01-13 18:00:00.000000",
            state=ScraperBinaryStatus.FINISHED,
            organization_id=test_organization_id + str(i),
        )
        await scraper_status_repository.save(sample_projection)

    output: dict[str, Any] | None = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-yyPepo0",
                "source": "steam_sales",
                "state": "FINISHED",
            },
            {
                "organization_id": "o-yyPepo1",
                "source": "steam_sales",
                "state": "FINISHED",
            },
        ],
        "draw": 1,
        "length": 2,
        "recordsFiltered": 6,
        "recordsTotal": 6,
        "start": 0,
    }


async def test_datatable_second_page(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
    test_organization_id,
) -> None:
    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, length=2, start=2
    )

    for i in range(6):
        sample_projection = scraper_status_factory(
            created_at="2025-01-13 18:00:00.000000",
            updated_at="2025-01-13 18:00:00.000000",
            state=ScraperBinaryStatus.FINISHED,
            organization_id=test_organization_id + str(i),
        )
        await scraper_status_repository.save(sample_projection)

    output: dict[str, Any] | None = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-yyPepo2",
                "source": "steam_sales",
                "state": "FINISHED",
            },
            {
                "organization_id": "o-yyPepo3",
                "source": "steam_sales",
                "state": "FINISHED",
            },
        ],
        "draw": 1,
        "length": 2,
        "recordsFiltered": 6,
        "recordsTotal": 6,
        "start": 2,
    }
