from typing import Any

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from scraper_service.datatables import DataTable
from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.repositories.scraper_status import _ScraperStatusModel


@pytest.fixture
def scraper_status_data_table(async_session: AsyncSession):
    return DataTable(session=async_session, table=_ScraperStatusModel)


async def test_datatable_single_column_order_desc(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
) -> None:
    sample_projection_1 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2025-01-13 18:00:00.000000",
        state=ScraperBinaryStatus.FINISHED,
    )
    sample_projection_2 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2024-01-13 18:00:00.000000",
        source="microsoft_sales",
        state=ScraperBinaryStatus.DISABLED,
    )

    await scraper_status_repository.save(sample_projection_1)
    await scraper_status_repository.save(sample_projection_2)

    columns = ["organization_id", "source", "state"]

    # sort by state ascending
    column_orders = [{"column": "2", "dir": "asc"}]

    query_params = create_query_params(column_names=columns, order=column_orders)

    output: dict[str, Any] | None = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "DISABLED",
            },
            {
                "organization_id": "o-AL6Tv7",
                "source": "steam_sales",
                "state": "FINISHED",
            },
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 2,
        "recordsTotal": 2,
        "start": 0,
    }


async def test_datatable_multi_column_orders(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
) -> None:
    sample_projection_1 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2025-01-13 18:00:00.000000",
        source="gog_sales",
        state=ScraperBinaryStatus.FINISHED,
    )
    sample_projection_2 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2024-01-13 18:00:00.000000",
        source="microsoft_sales",
        state=ScraperBinaryStatus.DISABLED,
    )

    await scraper_status_repository.save(sample_projection_1)
    await scraper_status_repository.save(sample_projection_2)

    columns = ["organization_id", "source", "state"]

    # sort by source descending and state ascending
    column_orders = [{"column": "1", "dir": "desc"}, {"column": "2", "dir": "asc"}]
    query_params = create_query_params(column_names=columns, order=column_orders)

    output: dict[str, Any] | None = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "DISABLED",
            },
            {"organization_id": "o-AL6Tv7", "source": "gog_sales", "state": "FINISHED"},
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 2,
        "recordsTotal": 2,
        "start": 0,
    }


async def test_datatable_order_should_properly_index_columns_with_empty_columns_provided(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
):
    await scraper_status_repository.save(
        scraper_status_factory(
            created_at="2025-01-13 18:00:00.000000",
            updated_at="2025-01-13 18:00:00.000000",
            source="app_store_sales",
            state=ScraperBinaryStatus.FINISHED,
        )
    )
    await scraper_status_repository.save(
        scraper_status_factory(
            created_at="2025-01-13 18:00:00.000000",
            updated_at="2024-01-13 18:00:00.000000",
            source="microsoft_sales",
            state=ScraperBinaryStatus.DISABLED,
        )
    )

    # this is a situation in which frontend defines "fake" columns to render some stuff but they send it in a query either way
    columns = ["", "", "organization_id", "source", "state"]

    # sort by source descending (index 3)
    query_params = create_query_params(
        column_names=columns, order=[{"column": "3", "dir": "desc"}]
    )

    output: dict[str, Any] | None = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "DISABLED",
            },
            {
                "organization_id": "o-AL6Tv7",
                "source": "app_store_sales",
                "state": "FINISHED",
            },
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 2,
        "recordsTotal": 2,
        "start": 0,
    }
