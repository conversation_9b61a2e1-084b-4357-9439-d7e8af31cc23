import zoneinfo
from datetime import datetime
from typing import Any

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from scraper_service.datatables import DataTable
from scraper_service.logic.repositories.scraper_status import _ScraperStatusModel


@pytest.fixture
def scraper_status_data_table(async_session: AsyncSession):
    return DataTable(session=async_session, table=_ScraperStatusModel)


@pytest.fixture(autouse=True)
async def _fill_scraper_status_test_database(
    scraper_status_repository, scraper_status_factory
) -> None:
    await scraper_status_repository.save(
        scraper_status_factory(
            created_at="2025-01-13 18:00:00.000000",
            updated_at="2025-01-13 18:00:00.000000",
            state="FAILED",
        )
    )
    await scraper_status_repository.save(
        scraper_status_factory(
            created_at="2025-01-13 18:00:00.000000",
            updated_at="2025-01-13 18:00:00.000000",
            source="microsoft_sales",
            state="FAILED",
        )
    )
    await scraper_status_repository.save(
        scraper_status_factory(
            created_at="2025-01-13 18:00:00.000000",
            updated_at="2025-01-13 18:00:00.000000",
            source="gog_sales",
            state="FINISHED",
        )
    )


async def test_datatable_search_builder_equals(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "=",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["microsoft_sales"],
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "FAILED",
            }
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 1,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_not_equals(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "!=",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["microsoft_sales"],
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {"organization_id": "o-AL6Tv7", "source": "steam_sales", "state": "FAILED"},
            {"organization_id": "o-AL6Tv7", "source": "gog_sales", "state": "FINISHED"},
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 2,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_starts(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "starts",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["microsoft"],
            }
        ],
        "logic": "AND",
    }
    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "FAILED",
            }
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 1,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_not_starts(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "!starts",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["microsoft"],
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {"organization_id": "o-AL6Tv7", "source": "steam_sales", "state": "FAILED"},
            {"organization_id": "o-AL6Tv7", "source": "gog_sales", "state": "FINISHED"},
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 2,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_contains(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "contains",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["soft"],
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "FAILED",
            }
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 1,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_not_contains(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "!contains",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["soft"],
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {"organization_id": "o-AL6Tv7", "source": "steam_sales", "state": "FAILED"},
            {"organization_id": "o-AL6Tv7", "source": "gog_sales", "state": "FINISHED"},
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 2,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_ends(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "ends",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["soft_sales"],
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "FAILED",
            }
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 1,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_not_ends(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "!contains",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["soft_sales"],
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {"organization_id": "o-AL6Tv7", "source": "steam_sales", "state": "FAILED"},
            {"organization_id": "o-AL6Tv7", "source": "gog_sales", "state": "FINISHED"},
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 2,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_null(
    scraper_status_data_table: DataTable, create_query_params
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "null",
                "data": "Source",
                "origData": "source",
                "type": "string",
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 0,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_not_null(
    scraper_status_data_table: DataTable,
    create_query_params,
) -> None:
    search_builder = {
        "criteria": [
            {
                "condition": "!null",
                "data": "Source",
                "origData": "source",
                "type": "string",
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {"organization_id": "o-AL6Tv7", "source": "steam_sales", "state": "FAILED"},
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "FAILED",
            },
            {"organization_id": "o-AL6Tv7", "source": "gog_sales", "state": "FINISHED"},
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 3,
        "recordsTotal": 3,
        "start": 0,
    }


async def test_datatable_search_builder_date_column(
    scraper_status_data_table: DataTable,
    create_query_params,
    scraper_status_repository,
    scraper_status_factory,
):
    await scraper_status_repository.save(
        scraper_status_factory(
            created_at="2024-01-01 00:00:00.000000",
            updated_at="2024-01-01 00:00:00.000000",
            source="app_store_sales",
            state="FAILED",
        )
    )
    search_builder = {
        "criteria": [
            {
                "condition": "<",
                "data": "Updated At",
                "origData": "updated_at",
                "type": "date",
                "value": "2025-01-01",
            }
        ],
        "logic": "AND",
    }

    column_names = ["organization_id", "updated_at", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search_builder=search_builder
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "app_store_sales",
                "updated_at": datetime(
                    2024, 1, 1, 0, 0, tzinfo=zoneinfo.ZoneInfo(key="Etc/UTC")
                ),
                "state": "FAILED",
            },
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 1,
        "recordsTotal": 4,
        "start": 0,
    }
