from scraper_service.datatables import DataTable


def test_datatable_parse_search_builder_params():
    input_data = {
        "searchBuilder[criteria][0][condition]": "!=",
        "searchBuilder[criteria][0][data]": "Source",
        "searchBuilder[criteria][0][origData]": "source",
        "searchBuilder[criteria][0][type]": "string",
        "searchBuilder[criteria][0][value][]": "app_store_sales",
        "searchBuilder[criteria][0][value1]": "app_store_sales",
        "searchBuilder[logic]": "AND",
    }

    expected_output = {
        "criteria": [
            {
                "condition": "!=",
                "data": "Source",
                "origData": "source",
                "type": "string",
                "value": ["app_store_sales"],
                "value1": "app_store_sales",
            }
        ],
        "logic": "AND",
    }
    assert DataTable._parse_search_builder_params(input_data) == expected_output


def test_datatable_parse_search_builder_params_null_condition():
    input_data = {
        "searchBuilder[criteria][0][condition]": "null",
        "searchBuilder[criteria][0][data]": "Source",
        "searchBuilder[criteria][0][origData]": "source",
        "searchBuilder[criteria][0][type]": "string",
        "searchBuilder[logic]": "AND",
    }
    expected_output = {
        "criteria": [
            {
                "condition": "null",
                "data": "Source",
                "origData": "source",
                "type": "string",
            }
        ],
        "logic": "AND",
    }
    assert DataTable._parse_search_builder_params(input_data) == expected_output
