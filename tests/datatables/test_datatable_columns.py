from typing import Any

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from scraper_service.datatables import DataTable
from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.repositories.scraper_status import _ScraperStatusModel


@pytest.fixture
def scraper_status_data_table(async_session: AsyncSession):
    return DataTable(session=async_session, table=_ScraperStatusModel)


async def test_datatable_columns(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
) -> None:
    sample_projection_1 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2025-01-13 18:00:00.000000",
        state=ScraperBinaryStatus.FAILED,
    )
    sample_projection_2 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2024-01-13 18:00:00.000000",
        source="microsoft_sales",
        state=ScraperBinaryStatus.FAILED,
    )

    await scraper_status_repository.save(sample_projection_1)
    await scraper_status_repository.save(sample_projection_2)

    query_params: dict[str, Any] = create_query_params(
        column_names=["organization_id", "source"], start=0, length=1
    )
    output: dict[str, Any] | None = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [{"organization_id": "o-AL6Tv7", "source": "microsoft_sales"}],
        "draw": 1,
        "length": 1,
        "recordsFiltered": 2,
        "recordsTotal": 2,
        "start": 0,
    }


async def test_datatable_columns_error(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
) -> None:
    invalid_columns: list[str] = ["organization_id", "source", "non_exisiting_column"]
    query_params: dict[str, Any] = create_query_params(
        column_names=invalid_columns, start=0, length=1
    )

    with pytest.raises(ValueError, match="No column non_exisiting_column in"):
        await scraper_status_data_table.execute(request_params=query_params)
