from typing import Any

import pytest

from scraper_service.datatables.base import Criteria


@pytest.fixture
def create_query_params():
    def _create_query_params(
        column_names: list[str],
        search: str = "",
        regex: bool = False,
        start: int = 0,
        length: int = 10,
        order: [dict[int, str]] = None,
        search_builder: Criteria = None,
    ) -> dict[str, Any]:
        params = {
            "draw": "1",
            "start": str(start),
            "length": str(length),
            "search[value]": str(search),
            "search[regex]": "true" if regex else "false",
        }

        for i, _item in enumerate(column_names):
            cols: str = f"columns[{i}]"
            params[f"{cols}[data]"] = _item
            params[f"{cols}[name]"] = _item
            params[f"{cols}[searchable]"] = "true"
            params[f"{cols}[orderable]"] = "true"
            params[f"{cols}[search][value]"] = ""
            params[f"{cols}[search][regex]"] = "false"

        for i, item in enumerate(order or [{"column": 0, "dir": "asc"}]):
            for key, value in item.items():
                params[f"order[{i}][{key}]"] = str(value)

        if search_builder:
            dc = encode_search_builder_criteria(search_builder)
            for k, v in dc.items():
                params[k] = v

        return params

    return _create_query_params


def encode_search_builder_criteria(data: Criteria, prefix="searchBuilder"):
    flat = {}

    def recurse(value, path):
        if isinstance(value, dict):
            for k, v in value.items():
                recurse(v, path + [k])
        elif isinstance(value, list):
            for i, item in enumerate(value):
                recurse(item, path + [str(i)])
        else:
            key = f"{prefix}[" + "][".join(path) + "]"
            flat[key] = value

    recurse(data, [])
    return flat
