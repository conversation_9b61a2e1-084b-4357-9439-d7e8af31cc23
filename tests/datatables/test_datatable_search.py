from typing import Any

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from scraper_service.datatables import DataTable
from scraper_service.logic.entities import ScraperBinaryStatus
from scraper_service.logic.repositories.scraper_status import _ScraperStatusModel


@pytest.fixture
def scraper_status_data_table(async_session: AsyncSession):
    return DataTable(session=async_session, table=_ScraperStatusModel)


async def test_datatable_global_search(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
    test_organization_id,
) -> None:
    sample_projection_1 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2025-01-13 18:00:00.000000",
        state=ScraperBinaryStatus.FAILED,
    )
    sample_projection_2 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2024-01-13 18:00:00.000000",
        source="microsoft_sales",
        state=ScraperBinaryStatus.FAILED,
    )

    await scraper_status_repository.save(sample_projection_1)
    await scraper_status_repository.save(sample_projection_2)

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(
        column_names=column_names, search="steam"
    )

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {"organization_id": "o-AL6Tv7", "source": "steam_sales", "state": "FAILED"}
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 1,
        "recordsTotal": 2,
        "start": 0,
    }


async def test_datatable_column_search(
    scraper_status_data_table: DataTable,
    scraper_status_repository,
    scraper_status_factory,
    create_query_params,
    test_organization_id,
) -> None:
    sample_projection_2 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2024-01-13 18:00:00.000000",
        organization_id=test_organization_id + "1",
        source="microsoft_sales",
        state=ScraperBinaryStatus.FAILED,
    )
    sample_projection_3 = scraper_status_factory(
        created_at="2025-01-13 18:00:00.000000",
        updated_at="2024-01-13 18:00:00.000000",
        source="microsoft_sales",
        state=ScraperBinaryStatus.FINISHED,
    )

    await scraper_status_repository.save(sample_projection_2)
    await scraper_status_repository.save(sample_projection_3)

    column_names = ["organization_id", "source", "state"]
    query_params: dict[str, Any] = create_query_params(column_names=column_names)

    source_column_index: int = column_names.index("source")
    state_column_index: int = column_names.index("state")
    query_params[f"columns[{source_column_index}][search][value]"] = "microsoft"
    query_params[f"columns[{state_column_index}][search][value]"] = "FINISH"

    output: dict[str, Any] = await scraper_status_data_table.execute(
        request_params=query_params
    )

    assert output == {
        "data": [
            {
                "organization_id": "o-AL6Tv7",
                "source": "microsoft_sales",
                "state": "FINISHED",
            }
        ],
        "draw": 1,
        "length": 10,
        "recordsFiltered": 1,
        "recordsTotal": 2,
        "start": 0,
    }
