import pytest
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Date, Integer, MetaData, String, Table

from scraper_service.datatables.search_builder import build_criteria


@pytest.fixture
def mock_table():
    metadata = MetaData()
    return Table(
        "test_table",
        metadata,
        Column("string_col", String),
        Column("num_col", Integer),
        Column("date_col", Date),
        <PERSON>umn("array_col", ARRAY(String)),
    )


def test_build_criteria_raises_error_for_invalid_condition(mock_table):
    criteria = [
        {
            "origData": "string_col",
            "condition": "invalid",
            "value": ["test"],
            "type": "string",
        }
    ]
    with pytest.raises(
        ValueError, match="Unsupported condition: invalid for type: string"
    ):
        build_criteria(mock_table, criteria, logic="AND")


def test_build_criteria_raises_error_for_invalid_column_type(mock_table):
    criteria = [
        {
            "origData": "string_col",
            "condition": "=",
            "value": ["test"],
            "type": "unsupported",
        }
    ]
    with pytest.raises(ValueError, match="Unsupported column type: unsupported"):
        build_criteria(mock_table, criteria, logic="AND")


def test_build_criteria_combines_multiple_conditions(mock_table):
    criteria = [
        {
            "origData": "string_col",
            "condition": "=",
            "value": ["test_value"],
            "type": "string",
        },
        {"origData": "num_col", "condition": ">", "value": [10], "type": "num"},
    ]
    expr = build_criteria(mock_table, criteria, logic="AND")
    assert (
        str(expr)
        == "test_table.string_col = :string_col_1 AND test_table.num_col > :num_col_1"
    )


def test_build_criteria_handles_nested_criteria(mock_table):
    criteria = [
        {
            "criteria": [
                {
                    "origData": "string_col",
                    "condition": "contains",
                    "value": ["test"],
                    "type": "string",
                },
                {"origData": "num_col", "condition": "<", "value": [50], "type": "num"},
            ],
            "logic": "OR",
        },
        {
            "origData": "date_col",
            "condition": "=",
            "value": ["2023-01-01"],
            "type": "date",
        },
    ]
    expr = build_criteria(mock_table, criteria, logic="AND")
    assert (
        str(expr)
        == "(test_table.string_col LIKE :string_col_1 OR test_table.num_col < :num_col_1) AND test_table.date_col = :date_col_1"
    )
