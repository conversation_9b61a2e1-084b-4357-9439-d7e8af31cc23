from enum import Enum
from functools import singledispatchmethod
from typing import Any, Self

import requests
from pydantic import BaseModel


class Status(str, Enum):
    STARTED = "started"
    FINISHED = "finished"
    FAILED = "failed"
    UNCONFIGURED = "unconfigured"


class ApplicationStarted(BaseModel):
    organization_id: str


class StatusChanged(BaseModel):
    new_status: Status


class OrganizationStatus:
    def __init__(self, events: list) -> None:
        for event in events:
            self.apply(event)

        self.changes: list[Any] = []

    @classmethod
    def create(cls, organization_id: str) -> Self:
        initial_event = ApplicationStarted(organization_id=organization_id)
        instance = cls([initial_event])
        instance.changes = [initial_event]
        return instance

    @singledispatchmethod
    def apply(self, event: BaseModel):
        raise ValueError("Unknown event!")

    @apply.register(ApplicationStarted)
    def _(self, event: ApplicationStarted):
        self.organization_id = event.organization_id
        self.status = Status.UNCONFIGURED

    @apply.register(StatusChanged)
    def _(self, event: StatusChanged):
        self.status: Status = event.new_status

    def set_status(self, new_status: Status):
        self.status = new_status

        event = StatusChanged(new_status=new_status)
        self.apply(event)
        self.changes.append(event)


def xtest_x():
    events_stream = [
        ApplicationStarted(organization_id="123"),
        StatusChanged(new_status=Status.STARTED),
        StatusChanged(new_status=Status.FINISHED),
        StatusChanged(new_status=Status.STARTED),
        StatusChanged(new_status=Status.FAILED),
    ]
    result = OrganizationStatus(events=events_stream)
    assert result.organization_id == "123"
    assert result.status == Status.FAILED


def xtest_2():
    result = OrganizationStatus.create(organization_id="123")
    result.set_status(Status.STARTED)

    assert result.status == Status.STARTED
    assert result.changes == [
        ApplicationStarted(organization_id="123"),
        StatusChanged(new_status=Status.STARTED),
    ]


# class BalanceView(ReadModel):
#     def __init__(self):
#         self._storage: Dict[UUID, Money] = {}

#     @singledispatchmethod
#     def handle(self, event) -> None:
#         pass

#     @handle.register
#     def _(self, event: AccountCreated) -> None:
#         self._storage[event.producer_id] = event.deposit

#     @handle.register
#     def _(self, event: MoneyWithdrawn) -> None:
#         self._storage[event.producer_id] -= event.amount

#     def for_account(self, account_id: UUID) -> AccountDTO:
#         return AccountDTO(account_id=account_id, balance=self._storage[account_id])


# def post(event):
#     repository.save(event)
#     balance_view.handle(event)
#     balance_view.uncommited_changes()


def xtest_teams():
    expected_msg = (
        "### Global platform issues:\n\n"
        "| source | affected |\n"
        "|--------|----------|\n"
        "| 🆕 source1 | 50 % affected |\n"
    )

    headers = {"Content-Type": "application/json"}
    webhook_url = "https://indiebisa.webhook.office.com/webhookb2/2da8a82e-4313-45b2-9a6c-1b67cff8b7b2@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/238f7edb7ad447e281eb403a8dc53afe/8c942920-d413-4a59-967c-8b469596af99/V2kI_IdBnWHdg66PLcZgg9yqQkcg2wC-kP5DsWpmBSrYU1"
    response = requests.post(
        webhook_url, json={"text": expected_msg}, headers=headers, timeout=60
    )
    assert response.status_code == 200
