import datetime
import logging
import time
from pathlib import Path

import factory
import jwt
import pytest
import pytest_asyncio
from alembic import command
from alembic.config import Config as AlembicConfig
from fastapi.testclient import TestClient
from httpx import ASGITransport, AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.asyncio.engine import AsyncEngine
from sqlalchemy_utils import create_database, database_exists, drop_database

from scraper_service.api.dependencies import (
    get_async_session,
    get_config,
    get_user_service_client,
)
from scraper_service.config import Config, ValidatedDatabaseURL
from scraper_service.connectors.user_service_client import UserServiceClient
from scraper_service.logic.entities import (
    DatabaseURL,
    DateRange,
    LoginState,
    Organization,
    ScraperBinaryStatus,
    Source,
    User,
)
from scraper_service.logic.events.login_state_changed import (
    LoginStateChangedBody,
    LoginStateChangedEvent,
)
from scraper_service.logic.events.scraper_state_changed import (
    <PERSON>raperStateChangedBody,
    ScraperStateChangedEvent,
)
from scraper_service.logic.models.scraper_operation_history import (
    ScraperOperationHistory,
)
from scraper_service.logic.models.scraper_status import ScraperStatus
from scraper_service.logic.repositories.scraper_status import ScraperStatusRepository
from scraper_service.main import app


@pytest.fixture(scope="session")
def anyio_backend():
    return "asyncio"


@pytest.fixture(scope="session")
def tests_path():
    return Path(__file__).parent.resolve()


@pytest.fixture(scope="session")
def project_root_path(tests_path):
    return tests_path.parent.resolve()


@pytest.fixture
def test_user_id():
    return "u-tWCKyO"


@pytest.fixture
def test_organization_id():
    return "o-yyPepo"


@pytest.fixture
def jwt_token_accesible_organization():
    return "o-yyPepo"


@pytest.fixture
def test_machine_info():
    return {
        "arch": "x64",
        "homedir": "/home/<USER>",
        "hostname": "tester",
        "platform": "linux",
        "release": "6.8.0-52-generic",
        "osType": "Linux",
        "osVersion": "#53~22.04.1-Ubuntu SMP PREEMPT_DYNAMIC Wed Jan 15 19:18:46 UTC 2",
    }


@pytest.fixture(scope="session")
def user_access_token_private_key(tests_path):
    with (tests_path / "static" / "user_access_token_private_key.pem").open() as f:
        return f.read()


@pytest.fixture(scope="session")
def user_access_token_public_key(tests_path):
    with (tests_path / "static" / "user_access_token_public_key.pem").open() as f:
        return f.read()


class AnyStringDate:
    """
    Custom matcher to check if a string is a valid ISO 8601 date.
    ie: 2023-10-01T12:00:00Z or 2023-10-01T12:00:00+00:00"""

    def __eq__(self, other) -> bool:
        try:
            datetime.datetime.fromisoformat(other.replace("Z", "+00:00"))
        except Exception:
            return False
        return True


@pytest.fixture
def any_string_date():
    return AnyStringDate()


@pytest.fixture(scope="session")
def test_config(user_access_token_public_key) -> Config:
    class FakeConfig(Config):
        env: str = "test"
        api_key: str = "ApiTest123!"
        database_url: ValidatedDatabaseURL = DatabaseURL(
            "postgresql+psycopg://indiebi:Password1!@localhost:5432/scraper_service_db"
        )

        database_pool_size: int = 1
        teams_webhook_url: str = "https://teams.com/webhook"

    return FakeConfig(
        user_access_token_public_key=user_access_token_public_key,
        user_service_url="https://user-service.test",
        user_service_key="us-key",
    )


@pytest.fixture(scope="session")
def prepare_database(test_config, project_root_path):
    logger = logging.getLogger("sqlalchemy")
    logger.setLevel(logging.ERROR)
    logger.addHandler(logging.StreamHandler())

    engine = create_engine(test_config.database_url)
    if database_exists(engine.url):
        drop_database(engine.url)

    create_database(engine.url)

    alembic_config = AlembicConfig()
    alembic_config.set_main_option("sqlalchemy.url", test_config.database_url)
    alembic_config.set_main_option(
        "script_location", str(project_root_path / "alembic_migrations")
    )
    command.upgrade(alembic_config, "head")

    return test_config.database_url


@pytest.fixture(scope="session")
def get_async_db_engine(test_config, prepare_database) -> AsyncEngine:
    return create_async_engine(test_config.database_url)


@pytest.fixture(scope="session")
def get_async_sessionmaker(get_async_db_engine):
    return async_sessionmaker(get_async_db_engine, expire_on_commit=False)


@pytest_asyncio.fixture
async def async_session(
    get_async_sessionmaker: async_sessionmaker[AsyncSession], anyio_backend
):
    session: AsyncSession = get_async_sessionmaker()
    yield session

    await session.rollback()


@pytest.fixture
def user_service_client(
    test_config: Config,
) -> UserServiceClient:
    repo = UserServiceClient(test_config)
    repo.get_organization_by_id = lambda org_id: Organization(
        id=org_id, name="Test Org"
    )
    repo.get_user_by_id = lambda user_id: User(id=user_id, email="<EMAIL>")
    return repo


@pytest.fixture
def unauthenticated_client(test_config, async_session, user_service_client):
    old_dependencies = app.dependency_overrides.copy()
    app.dependency_overrides[get_async_session] = lambda: (yield async_session)
    app.dependency_overrides[get_config] = lambda: test_config
    app.dependency_overrides[get_user_service_client] = lambda: user_service_client

    yield TestClient(app)

    app.dependency_overrides = old_dependencies


@pytest.fixture
def api_key_client(unauthenticated_client):
    unauthenticated_client.headers.update({"x-api-key": "ApiTest123!"})
    return unauthenticated_client


@pytest.fixture
def jwt_client(
    unauthenticated_client, user_access_token_private_key, auth_token_contents
):
    token = jwt.encode(
        auth_token_contents, user_access_token_private_key, algorithm="RS256"
    )
    unauthenticated_client.headers.update({"Authorization": f"Bearer {token}"})
    return unauthenticated_client


@pytest.fixture
async def async_unauthenticated_client(test_config, async_session, user_service_client):
    old_dependencies = app.dependency_overrides.copy()
    app.dependency_overrides[get_async_session] = lambda: (yield async_session)
    app.dependency_overrides[get_config] = lambda: test_config
    app.dependency_overrides[get_user_service_client] = lambda: user_service_client

    client = AsyncClient(transport=ASGITransport(app=app), base_url="http://test")
    yield client

    app.dependency_overrides = old_dependencies
    await client.aclose()


@pytest.fixture
async def async_api_key_client(
    async_unauthenticated_client: AsyncClient,
) -> AsyncClient:
    async_unauthenticated_client.headers.update({"x-api-key": "ApiTest123!"})
    return async_unauthenticated_client


@pytest.fixture
async def async_jwt_client(
    async_unauthenticated_client,
    test_config,
    async_session,
    user_access_token_private_key,
    auth_token_contents,
):
    token = jwt.encode(
        auth_token_contents, user_access_token_private_key, algorithm="RS256"
    )
    async_unauthenticated_client.headers.update({"Authorization": f"Bearer {token}"})

    return async_unauthenticated_client


@pytest.fixture
async def async_almost_expired_jwt_client(
    async_jwt_client,
    user_access_token_private_key,
    almost_expired_auth_token_contents,
):
    token = jwt.encode(
        almost_expired_auth_token_contents,
        user_access_token_private_key,
        algorithm="RS256",
    )
    async_jwt_client.headers.update({"Authorization": f"Bearer {token}"})
    return async_jwt_client


@pytest.fixture
def scraper_state_changed_event_factory(
    test_organization_id, test_user_id, test_machine_info
):
    def _factory_function(
        client_timestamp: datetime.datetime = datetime.datetime.now(),
        user_id: str = test_user_id,
        origin_id: str = "fake-1.0.0",
        source: Source = "steam_sales",
        organization_id: str = test_organization_id,
        account_identifier: str = "account_identifier_example",
        state: ScraperBinaryStatus = ScraperBinaryStatus.STARTED,
        operation_id: str = "12390019239abc",
        received_at: datetime.datetime = datetime.datetime.now(tz=datetime.UTC),
        machine_info: dict | None = None,
        reason: str | None = None,
        date_ranges: list[DateRange] | None = None,
    ) -> ScraperStateChangedEvent:
        if machine_info is None:
            machine_info = test_machine_info

        return ScraperStateChangedEvent(
            client_timestamp=client_timestamp,
            user_id=user_id,
            origin=origin_id,
            organization_id=organization_id,
            received_at=received_at,
            operation_id=operation_id,
            body=ScraperStateChangedBody(
                new_state=state,
                source=source,
                account_identifier=account_identifier,
                reason=reason,
                machine_info=machine_info,
                date_ranges=date_ranges,
            ),
        )

    return _factory_function


class LoginStateChangedBodyFactory(factory.Factory):
    class Meta:
        model = LoginStateChangedBody

    new_state: LoginState = LoginState.CONFIGURED
    is_manual: bool = False
    source: Source = "steam_sales"
    account_identifier: str = "account_identifier_example"

    reason: str | None = None
    machine_info: dict = {"hostname": "tester"}


class LoginStateChangedEventFactory(factory.Factory):
    class Meta:
        model = LoginStateChangedEvent

    received_at = "2024-01-01T10:01:00Z"
    client_timestamp = "2024-01-01T10:00:00Z"
    user_id: str = "u-tWCKyO"
    organization_id: str = "o-yyPepo"
    origin = "fake-1.0.0"
    operation_id: str = "login_operation_123"

    body = factory.SubFactory(LoginStateChangedBodyFactory)


@pytest.fixture
def login_state_changed_event_factory() -> type[LoginStateChangedEventFactory]:
    return LoginStateChangedEventFactory


class ScraperOperationHistoryFactory(factory.Factory):
    class Meta:
        model = ScraperOperationHistory

    organization_id = "o-yyPepo"
    source = "steam_sales"
    operation_id = factory.Faker("uuid4")
    user_id = "u-tWCKyO"
    state = "UNCONFIGURED"
    created_at = factory.LazyFunction(datetime.datetime.now)
    updated_at = factory.LazyFunction(datetime.datetime.now)
    start_timestamp = None
    end_timestamp = None


@pytest.fixture
def scraping_operation_history_factory():
    return ScraperOperationHistoryFactory


@pytest.fixture
def scraper_status_factory(test_user_id):
    class ScraperStatusFactory(factory.Factory):
        class Meta:
            model = ScraperStatus

        organization_id = "o-AL6Tv7"
        user_id = test_user_id
        source = "steam_sales"
        state = "UNCONFIGURED"
        created_at = factory.LazyFunction(datetime.datetime.now)
        updated_at = factory.LazyFunction(datetime.datetime.now)
        consecutive_failed_scrape_count = 0
        last_success_timestamp = None
        last_fail_timestamp = None
        last_fail_reason = None
        last_origin = None
        last_operation_id = None
        version = 0

    return ScraperStatusFactory


@pytest.fixture
def auth_token_contents():
    now_timestamp = int(time.time())
    return {
        "id": 10554,
        "email": "<EMAIL>",
        "user_id": "u-tWCKyO",
        "permissions": [
            {
                "organization_id": "o-yyPepo",
                "name": "REPORT_SERVICE_READ_REPORTS",
                "filters": [],
            },
            {
                "organization_id": "o-yyPepo",
                "name": "REPORT_SERVICE_READ_SKUS",
                "filters": [],
            },
            {
                "organization_id": "o-yyPepo",
                "name": "DATASET_MANAGER_VIEW_DASHBOARDS",
                "filters": [],
            },
            {
                "organization_id": "o-yyPepo",
                "name": "REPORT_SERVICE_MODIFY_REPORTS",
                "filters": [],
            },
            {
                "organization_id": "o-yyPepo",
                "name": "REPORT_SERVICE_MODIFY_SKUS",
                "filters": [],
            },
            {
                "organization_id": "o-yyPepo",
                "name": "REPORT_SERVICE_UPLOAD_REPORTS",
                "filters": [],
            },
            {
                "organization_id": "o-yyPepo",
                "name": "USER_SERVICE_V2_INVITE_USER",
                "filters": [],
            },
            {
                "organization_id": "o-yyPepo",
                "name": "USER_SERVICE_V2_ASSIGN_PERMISSIONS",
                "filters": [],
            },
            {
                "organization_id": "o-yyPepo",
                "name": "DESKTOP_APP_LEGACY_PARENT",
                "filters": [],
            },
        ],
        "exp": now_timestamp + 24 * 60 * 60,
        "iat": now_timestamp,
        "iss": "indiebi:user_service",
        "impersonator_app_id": "4ad7df87-6c81-44d4-ac4e-1563cc905b21",
        "impersonator_email": "<EMAIL>",
    }


@pytest.fixture
def almost_expired_auth_token_contents(auth_token_contents):
    auth_token_contents["exp"] = int(
        (datetime.datetime.now() - datetime.timedelta(hours=2, minutes=59)).timestamp()
    )
    return auth_token_contents


@pytest.fixture
def scraper_status_repository(
    async_session: AsyncSession,
) -> ScraperStatusRepository:
    return ScraperStatusRepository(
        session=async_session,
    )
