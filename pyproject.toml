[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe.tasks]
ruff-format = "ruff format ."
ruff-check = "ruff check --fix ."
lint = ["ruff-format", "ruff-check"]
format = "ruff format"
check = ["lint", "format", "test"]
db = { shell = "./scripts/ensure_db_is_ready.sh" }
clear_db = ["_stop", "_rm_volume"]
hooks = "poetry run pre-commit install"
test = ["db", "_test"]
start = ["db", "_start"]
_start = "uvicorn scraper_service.main:app --reload"
_stop = "docker compose down"
_rm_volume = "docker compose down --volumes"
_test = "pytest"


[tool.poe.executor]
type = "poetry"

[tool.poetry]
name = "scraper_service"
version = "0.1.0"
description = "Backend for Scraper Service"
authors = ["DPT <<EMAIL>>"]
readme = "README.md"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "supplemental"

[tool.poetry.dependencies]
python = "^3.12.4"
fastapi = "^0.115.3"
uvicorn = { extras = ["standard"], version = "^0.20.0" }
msal = "^1.23.0"
pyjwt = "^2.8.0"
logging-json = "^0.4.0"
sentry-sdk = "^1.40.1"
poethepoet = "^0.24.4"
ecs-logging = "^2.1.0"
pydantic = "^2.8.2"
pydantic-settings = "^2.3.4"
elastic-apm = "^6.21.3"
deprecated = "^1.2.14"
psycopg = { extras = ["binary"], version = "^3.2.6" }
psutil = "^6.1.0"
python-dotenv = "^1.0.1"
alembic = "^1.14.0"
sqlalchemy-utils = "^0.41.2"
sqlalchemy = "^2.0.36"
azure-identity = "^1.19.0"
apscheduler = "3.11.0"
httpx = "^0.23.3"
python-dateutil = "^2.9.0.post0"
querystring-parser = "^1.2.4"

[tool.poetry.group.dev.dependencies]
pytest = "^8.2.1"
pytest-random-order = "^1.1.1"
factory-boy = "^3.3.1"
time-machine = "^2.9.0"
ruff = "^0.4.5"
pytest-deadfixtures = "^2.2.1"
humanize = "^4.10.0"
typer = "^0.12.3"
locust = "^2.33.1"
pytest-asyncio = "^0.25.2"
pre-commit = "^3.6.0"
freezegun = "^1.5.1"
anyio = "^4.9.0"

[tool.pytest.ini_options]
addopts = "-vvv --color=yes"
testpaths = ["tests"]
filterwarnings = "ignore::DeprecationWarning"

[tool.ruff]
target-version = "py312"
preview = true
line-length = 88
src = [".", "tests"]

[tool.ruff.lint]
select = [
    "F",     # Pyflakes
    "E",     # Pycodestyle errors
    "W",     # Pycodestyle warnings
    "I",     # isort
    "ASYNC", # flake8-async (asyncio)
    "S",     # flake8-bandit (security)
    "ERA",   # flake8-eradicate (remove commented out code)
    "FLY",   # flynt (f-string)
    "PTH",   # flake8-use-pathlib
    "A",     # flake8-builtins
    "C4",    # flake8-comprehensions
    "EXE",   # flake8-executable
    "LOG",   # flake8-logging
    "INP",   # flake8-no-pep420 (no implicit namespace packages)
    "T20",   # flake8-print (print statements)
    "PT",    # flake8-pytest-style (pytest specific style issues)
    "RSE",   # flake8-raise (issues with raise statements)
    "SIM",   # flake8-simplify (simplifiable constructs)
    "C90",   # mccabe (complexity checker)
    "N",     # pep8-naming (naming conventions)
    "UP",    # pyupgrade (Python syntax upgrades)
    "TRY",   # tryceratops (anti-patterns in try/except blocks)
    "PGH",   # pygrep-hooks
    # "FBT",   # flake8-boolean-trap
    # "B",     # flake8-bugbear
    # "DTZ",   # flake8-datetimez
    # "ISC",   # flake8-implicit-str-concat
]
ignore = [
    "E501",   # Line too long
    "E712",   # Comparison to False should be 'if cond is False:' or 'if not cond:'
    "N818",   # Exception name {name} should be named with an Error suffix
    "S311",   # Standard pseudo-random generators are not suitable for security/cryptographic purposes
    "TRY003", # Avoid specifying long messages outside the exception class
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = [
    "S101",   # Bandit: Use of assert detected
    "INP001", # Implicit namespace package
]
"scripts/*" = [
    "T201", # Checks for print statements.
]

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = false

[tool.ruff.lint.isort]
known-first-party = ["scraper_service"]
