# Architecture Guidelines

## Data Flow Architecture

This document illustrates the overall data flow architecture of the scraper service, following the Command Query Responsibility Segregation (CQRS) and Event Sourcing patterns.

## Core Components Overview

The system is built around several key architectural components:

- **API Endpoints**: FastAPI routes that receive external data & provide data for other services (i.e. Dataoffice)
- **Commands**: Represent actions to be performed in the system
- **ScraperApplication**: Central orchestrator that handles commands and coordinates the system
- **Events**: Immutable records of what happened in the system
- **Projections**: Read models that are updated based on events
- **Repositories**: Data access layer for both events and projections
- **Models**: Domain entities that encapsulate business logic

## Data Flow Diagram

```mermaid
graph TD
    %% External Input - Event Producers
    SCRAPER["Event Producer<br>(i.e. ScraperLib)"] --> |"HTTP POST (Public API)"| API[API Endpoints]

    %% Command Processing
    API --> |creates| CMD[Command]
    CMD --> |handled by| APP[ScraperApplication]

    %% Event Flow
    APP --> |generates| EVENT[Event]
    APP --> |stores| ER[EventsRepository]
    ER --> |persists| EVENT_DB[(Event DB Table)]

    %% Projection Updates
    APP --> |notifies| PROJ[Projections]
    PROJ --> |updates| MODEL[Domain Model]
    MODEL --> |persisted via| REPO[Repository]
    REPO --> |saves to| READ_DB[(Projection DB Table)]

    %% Query Side - Data Consumers
    QUERY_API["Query Endpoints (Auth Required)"] --> |reads from| REPO
    DATAOFFICE["Data Consumer<br> (i.e. DataOffice)"] --> |"HTTP GET (Auth API)"| QUERY_API

    %% Styling - Data Flow Colors
    classDef input fill:#2196F3,stroke:#1976D2,stroke-width:2px,color:#fff
    classDef command fill:#9C27B0,stroke:#7B1FA2,stroke-width:2px,color:#fff
    classDef event fill:#FF9800,stroke:#F57C00,stroke-width:2px,color:#fff
    classDef projection fill:#4CAF50,stroke:#388E3C,stroke-width:2px,color:#fff
    classDef persistence fill:#F44336,stroke:#D32F2F,stroke-width:2px,color:#fff
    classDef database fill:#607D8B,stroke:#455A64,stroke-width:2px,color:#fff
    classDef output fill:#03A9F4,stroke:#0288D1,stroke-width:2px,color:#fff

    class SCRAPER,API input
    class CMD,APP command
    class EVENT,ER,EVENT_DB event
    class PROJ,MODEL projection
    class REPO persistence
    class READ_DB database
    class QUERY_API,DATAOFFICE output
```

## Color Legend - Data Flow

The diagram colors represent the **flow of data** through the system:

```mermaid
graph LR
    A[🔵 INPUT] --> B[🟣 COMMAND] --> C[🟠 EVENT] --> D[🟢 PROJECTION] --> E[🔴 PERSISTENCE] --> F[⚪ STORAGE]
    F --> G[🟦 OUTPUT]

    classDef input fill:#2196F3,stroke:#1976D2,stroke-width:2px,color:#fff
    classDef command fill:#9C27B0,stroke:#7B1FA2,stroke-width:2px,color:#fff
    classDef event fill:#FF9800,stroke:#F57C00,stroke-width:2px,color:#fff
    classDef projection fill:#4CAF50,stroke:#388E3C,stroke-width:2px,color:#fff
    classDef persistence fill:#F44336,stroke:#D32F2F,stroke-width:2px,color:#fff
    classDef database fill:#607D8B,stroke:#455A64,stroke-width:2px,color:#fff
    classDef output fill:#03A9F4,stroke:#0288D1,stroke-width:2px,color:#fff

    class A input
    class B command
    class C event
    class D projection
    class E persistence
    class F database
    class G output
```

| Color | Stage | Components | Purpose |
|-------|-------|------------|---------|
| 🔵 **Blue** | **INPUT** | Client, API Endpoints | Data enters the system |
| 🟣 **Purple** | **COMMAND** | Command, ScraperApplication | Processing and decision making |
| 🟠 **Orange** | **EVENT** | Event, EventsRepository, Event Store | Event generation and storage |
| 🟢 **Green** | **PROJECTION** | Projections, Domain Models | Processing events into business models |
| 🔴 **Red** | **PERSISTENCE** | Repositories | Saving processed data |
| ⚪ **Gray** | **STORAGE** | Read Databases | Physical data storage |
| 🟦 **Light Blue** | **OUTPUT** | Query Endpoints | Data leaves the system |


## Implementation Details

### 1. API Endpoint to Command

```mermaid
graph LR
    API[API Endpoint] --> |creates| CMD[Command]
    CMD --> |handled by| APP[ScraperApplication]

    classDef input fill:#2196F3,stroke:#1976D2,stroke-width:2px,color:#fff
    classDef command fill:#9C27B0,stroke:#7B1FA2,stroke-width:2px,color:#fff

    class API input
    class CMD,APP command
```

**Example: Receiving scraper events**

*File: [scraper_service/api/routes/scraper_event_routes.py](../scraper_service/api/routes/scraper_event_routes.py)*

API endpoint receives HTTP requests and converts them to commands:

```python
@external_router.post("/scraper_events")
async def register_new_scraper_event(
    event: AnyKnownEvent,
    application: ScraperApplicationDependency,
    current_user=Depends(get_current_user),
):
    command = HandleEventCommand(event=event, current_user=current_user)
    return await application.handle(command)
```

*File: [scraper_service/logic/scraper.py](../scraper_service/logic/scraper.py)*

The `HandleEventCommand` wraps the incoming event with user context and is processed by the application:

```python
class HandleEventCommand(Command):
    event: Event
    current_user: dict | None = None

# This command is then handled by ScraperApplication.handle(command)
```

### 2. Command Processing and Event Storage

```mermaid
graph LR
    APP[ScraperApplication] --> |stores| ER[EventsRepository]
    ER --> |persists| DB[(Event DB Table)]

    classDef command fill:#9C27B0,stroke:#7B1FA2,stroke-width:2px,color:#fff
    classDef event fill:#FF9800,stroke:#F57C00,stroke-width:2px,color:#fff
    classDef database fill:#607D8B,stroke:#455A64,stroke-width:2px,color:#fff

    class APP command
    class ER event
    class DB database
```

**Example: Command handler storing events**

*File: [scraper_service/logic/scraper.py](../scraper_service/logic/scraper.py)*

ScraperApplication processes commands using single dispatch pattern:

```python
class ScraperApplication:
    def __init__(self, events_repository: EventsRepository,  ...) -> None:
        # ...
        self._events_repository: EventsRepository = events_repository

    # ... some other code ...


    @singledispatchmethod
    async def handle(self, command: Command) -> None:
        raise NotImplementedError

    @handle.register
    async def _(self, command: HandleEventCommand) -> None:
        event = command.event

        # 1. place for some adjustments to the event
        # ...

        # 2. Store event in 'event' database table
        await self._events_repository.add(event=event)

        # 3. Apply to projections
        await self._apply_to_projections(event)

    @handle.register
    async def _(self, command: BlockCommand | UnblockCommand) -> None:
        # 1. Event creation
        event = ScraperBlockadeChangedEvent(
            # ... some fields
        )

        # 2. Store event in 'event' database table
        await self._events_repository.add(event=event)

        # 3. Apply to projections
        await self._apply_to_projections(event)

```

*File: [scraper_service/logic/events/event.py](../scraper_service/logic/events/event.py)*

Events are persisted to the database as immutable records:

```python
class EventsRepository:
    async def add(self, event: Event) -> None:
        new_event = _EventModel(**event.model_dump())
        self._session.add(new_event)
        await self._session.flush()
```

### 3. Event to Projection Updates

```mermaid
graph LR
    APP[ScraperApplication] --> |notifies| PROJ[Projection]
    PROJ --> |updates| MODEL[Domain Model]

    classDef command fill:#9C27B0,stroke:#7B1FA2,stroke-width:2px,color:#fff
    classDef projection fill:#4CAF50,stroke:#388E3C,stroke-width:2px,color:#fff

    class APP command
    class PROJ,MODEL projection
```

**Example: ScraperStatus projection handling events**

*File: [scraper_service/logic/scraper.py](../scraper_service/logic/scraper.py)*

ScraperApplication maintains a registry of projections for each event type:

```python
def register(self, projection: Projection, *events: type[Event]) -> None:
    for e in events:
        self._handlers[e].append(projection)

async def _apply_to_projections(self, event: Event):
    for projection in self._projections_for(event):
        await projection.handle(event)
```

*File: [scraper_service/logic/projections/scraper_status.py](../scraper_service/logic/projections/scraper_status.py)*

ScraperStatusProjection handles specific events and updates the model:

```python
class ScraperStatusProjection(Projection):
    @singledispatchmethod
    async def handle(self, event) -> None:
        raise NotImplementedError

    @handle.register
    async def _(self, event: ScraperStateChangedEvent) -> None:
        # Get current model state
        entity: ScraperStatus = await self._repo.get(
            organization_id=event.organization_id,
            user_id=event.user_id,
            source=event.body.source,
        )
        # Apply business logic
        entity.apply(event)
        # Save updated state
        await self._repo.save(entity)
```

### 4. Model Business Logic and Repository Persistence

```mermaid
graph LR
    MODEL[Domain Model] --> |persisted via| REPO[Repository]
    REPO --> |saves to| DB[(Projection DB Table)]

    classDef projection fill:#4CAF50,stroke:#388E3C,stroke-width:2px,color:#fff
    classDef persistence fill:#F44336,stroke:#D32F2F,stroke-width:2px,color:#fff
    classDef database fill:#607D8B,stroke:#455A64,stroke-width:2px,color:#fff

    class MODEL projection
    class REPO persistence
    class DB database
```

**Example: ScraperStatus model with business logic**

*File: [scraper_service/logic/models/scraper_status.py](../scraper_service/logic/models/scraper_status.py)*

Domain models encapsulate business rules and state transitions:

```python
class ScraperStatus(BaseModel):
    organization_id: OrganizationID
    user_id: UserID
    source: Source
    state: ScraperBinaryStatus = ScraperBinaryStatus.UNCONFIGURED
    consecutive_failed_scrape_count: int = 0
    last_success_timestamp: datetime | None = None

    @singledispatchmethod
    def apply(self, event) -> None:
        raise NotImplementedError

    @apply.register
    def _(self, event: ScraperStateChangedEvent) -> None:
        self.state = event.body.new_state
        if event.body.new_state == ScraperBinaryStatus.SCRAPE_SUCCESS:
            self.consecutive_failed_scrape_count = 0
            self.last_success_timestamp = event.client_timestamp
        elif event.body.new_state == ScraperBinaryStatus.SCRAPE_FAILED:
            self.consecutive_failed_scrape_count += 1
```

*File: [scraper_service/logic/repositories/scraper_status.py](../scraper_service/logic/repositories/scraper_status.py)*

Repository handles persistence with optimized database operations:

```python
class ScraperStatusRepository:
    async def save(self, entity: ScraperStatus) -> None:
        stmt = select(_ScraperStatusModel).where(
            _ScraperStatusModel.organization_id == entity.organization_id,
            _ScraperStatusModel.user_id == entity.user_id,
            _ScraperStatusModel.source == entity.source,
        )
        result = await self._session.execute(stmt)
        existing_entity = result.one_or_none()

        if existing_entity is not None:
            # Update existing record
            for field, value in entity.model_dump().items():
                setattr(existing_entity[0], field, value)
        else:
            # Create new record
            new_entity = _ScraperStatusModel(**entity.model_dump())
            self._session.add(new_entity)
```

### 5. Query Side - Reading Data

```mermaid
graph LR
    API[Query Endpoint] --> |reads from| REPO[Repository]
    REPO --> |queries| DB[(Projection DB Table)]

    classDef output fill:#03A9F4,stroke:#0288D1,stroke-width:2px,color:#fff
    classDef persistence fill:#F44336,stroke:#D32F2F,stroke-width:2px,color:#fff
    classDef database fill:#607D8B,stroke:#455A64,stroke-width:2px,color:#fff

    class API output
    class REPO persistence
    class DB database
```

**Example: Reading scraper status data**

*File: [scraper_service/api/routes/scraper_state_routes.py](../scraper_service/api/routes/scraper_state_routes.py)*

Query endpoints directly access repositories for optimized reads:

```python
@internal_router.get("/scraper_status")
async def get_scraper_status(
    status_repo: ScraperStatusRepositoryDependency,
    organization_id: OrganizationID,
    source: Source | None = None,
):
    return await status_repo.get_datatable_format({
        "organization_id": organization_id,
        "source": source
    })
```


This architecture ensures:
- **Scalability**: Read and write operations are separated
- **Auditability**: Complete event history is maintained
- **Flexibility**: New projections can be added without affecting existing code
- **Consistency**: Single source of truth through event sourcing
