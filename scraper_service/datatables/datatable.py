import random
import re
from typing import Any
from urllib.parse import urlencode

from querystring_parser import parser
from sqlalchemy import Result, Select, Subquery, and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.sql.elements import ColumnElement, KeyedColumnElement

from scraper_service.datatables.base import (
    DTColumn,
    DTColumnOrder,
    DTDataCallbacks,
    DTParams,
)
from scraper_service.datatables.search_builder import build_criteria


class DataTable:
    """
    Sqlalchemy ORM-compatible data table class.
    See https://www.datatables.net/manual/server-side#API

    :param request_params: dict[str, Any] - the  query parameters sent via the jQuery datatables ajax request
    :param session: Session -  sqlalchemy database session
    :param table: table: FromClause - sqlalchemy FromClause
    :param column_names: list[str] - table column names to display in the datatable, used for projection in sql
    :param callbacks: DTDataCallbacks  - callback that populate  DT_ROW_ID, DT_ROW_CLASS, DT_ROW_ATTR, DT_ROW_DATA
    :attr params: DTParams - parsed request parameters to use for result filtering, projection, sorting and paging
    :attr recordsTotal: int -  the total number of records available in this model/table
    :attr recordsFiltered: int - the number of records for the filtered result (before pagination)
    :attr data: list[dict] - the list of data objects sent to the datatable
    :attr error: str -  if there was an error with data retrieval, this the error message will be sent instead
    """

    callbacks: DTDataCallbacks | None
    session: AsyncSession
    table: type[DeclarativeBase]
    column_names: list[str]
    params: DTParams
    search_builder: dict[str, Any] | None = None
    records_total: int = 0
    records_filtered: int = 0
    data: list[dict[str, Any]] = []
    error: str | None = None

    def __init__(
        self,
        table: type[DeclarativeBase],
        session: AsyncSession,
    ):
        self.table = table
        self.session = session

    @staticmethod
    def _parse_order(request_params: dict[str, Any]) -> list[DTColumnOrder]:
        """Parse the order[index][*] parameters"""
        order: list[DTColumnOrder] = []
        order_pattern = re.compile(r"order\[(.*?)]\[column]")
        order_params: dict[str, Any] = {
            k: v for k, v in request_params.items() if order_pattern.match(k)
        }

        for i in range(len(order_params)):
            dt_column_order: DTColumnOrder = DTColumnOrder(
                column_index=int(request_params[f"order[{i}][column]"]),
                is_asc=request_params[f"order[{i}][dir]"] == "asc",
            )
            order.append(dt_column_order)
        return order

    @staticmethod
    def _parse_columns(request_params: dict[str, Any]) -> list[DTColumn]:
        """Parse the column[index][*] parameters"""
        columns: list[DTColumn] = []
        data_pattern = re.compile(r"columns\[(.*?)]\[data]")
        # Extract only the keys of type columns[i][data] from the params
        data_param: dict[str, Any] = {
            k: v for k, v in request_params.items() if data_pattern.match(k)
        }

        for i in range(len(data_param)):
            column: DTColumn = DTColumn(
                index=i,
                data=data_param.get(f"columns[{i}][data]"),
                name=request_params.get(f"columns[{i}][name]"),
                searchable=request_params.get(f"columns[{i}][searchable]") == "true",
                orderable=request_params.get(f"columns[{i}][orderable]") == "true",
                search_value=request_params.get(f"columns[{i}][search][value]"),
                search_regex=request_params.get(f"columns[{i}][search][regex]")
                == "true",
            )
            columns.append(column)
        return columns

    @staticmethod
    def _parse_search_builder_params(request_params: dict[str, Any]) -> dict[str, Any]:
        search_builder_params = {
            k: v for k, v in request_params.items() if k.startswith("searchBuilder")
        }
        parsed_filter = parser.parse(urlencode(search_builder_params))
        return (
            DataTable._normalize_dict_to_list(parsed_filter["searchBuilder"])
            if parsed_filter
            else None
        )

    @staticmethod
    def _normalize_dict_to_list(data):
        if isinstance(data, dict):
            # Handle dicts with integer keys like a list
            if all(isinstance(k, int) for k in data):
                try:
                    return [
                        DataTable._normalize_dict_to_list(data[k]) for k in sorted(data)
                    ]
                except KeyError:
                    pass
            # Optional: Handle edge case like {'': 'google_sales'} as ['google_sales']
            if len(data) == 1 and "" in data:
                return [DataTable._normalize_dict_to_list(data[""])]
            return {k: DataTable._normalize_dict_to_list(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [DataTable._normalize_dict_to_list(elem) for elem in data]
        else:
            return data

    @staticmethod
    def _parse_params(request_params: dict[str, Any]) -> DTParams:
        """Parse the request (query) parameters"""
        params = DTParams(
            draw=int(request_params.get("draw", random.randint(1, 1000))),
            start=int(request_params.get("start", 0)),
            length=int(request_params.get("length", -1)),
            search_value=request_params.get("search[value]", ""),
            search_regex=request_params.get("search[regex]") == "true",
            columns=DataTable._parse_columns(request_params),
            order=DataTable._parse_order(request_params),
        )

        return params

    async def _get_records_total(self) -> int:
        stmt = select(func.count()).select_from(self.table)
        result = await self.session.execute(stmt)
        result = result.scalar()
        return 0 if result is None else result

    async def _get_records_filtered(self, subquery: Subquery) -> int:
        result: int | None = await self.session.scalar(
            select(func.count()).select_from(subquery)
        )
        return 0 if result is None else result

    def _get_table_column_by_index(self, index: int) -> KeyedColumnElement[Any]:
        column = self.params.columns[index]
        return self.table.__table__.columns[column.data]

    def _add_order_criteria(self, stmt: Select[Any]) -> Select[Any]:
        """Add order by criteria to select statement"""
        for order in self.params.order:
            column: KeyedColumnElement[Any] = self._get_table_column_by_index(
                order.column_index
            )
            stmt = (
                stmt.order_by(column.asc())
                if order.is_asc
                else stmt.order_by(column.desc())
            )
        return stmt

    def _add_search_builder_criteria(self, stmt: Select[Any]) -> Select[Any]:
        """Add search query builder criteria to select statement"""
        if self.search_builder:
            # Build the SQLAlchemy filter from the parsed search builder criteria
            expressions = build_criteria(
                self.table.__table__,
                self.search_builder["criteria"],
                self.search_builder["logic"],
            )
            stmt = stmt.where(expressions)
        return stmt

    def _add_global_search_criterion(self, stmt: Select[Any]) -> Select[Any]:
        """Add global search filter across all searchable columns to select statement"""
        expressions: list[ColumnElement[Any]] = []
        for dt_col in self.params.columns:
            if dt_col.searchable:
                column: KeyedColumnElement[Any] = self._get_table_column_by_index(
                    dt_col.index
                )
                col_element: ColumnElement[Any]
                if self.params.search_regex:
                    regex: str = self.params.search_value
                    col_element = column.regexp_match(regex)
                else:
                    col_element = column.like(f"%{self.params.search_value}%")
                expressions.append(col_element)
        return stmt.where(or_(*expressions)) if len(expressions) > 0 else stmt

    def _add_column_search_criteria(self, stmt: Select[Any]) -> Select[Any]:
        """add the individual column filters to select statement"""
        expressions: list[ColumnElement[Any]] = []
        for dt_col in self.params.columns:
            if dt_col.search_value:
                column: KeyedColumnElement[Any] = self._get_table_column_by_index(
                    dt_col.index
                )
                if self.params.search_regex:
                    col_element = column.regexp_match(dt_col.search_value)
                else:
                    col_element = column.like(f"%{dt_col.search_value}%")
                expressions.append(col_element)
        return stmt.where(and_(*expressions)) if len(expressions) > 0 else stmt

    def _built_select_statement(self) -> Select[Any]:
        stmt: Select[Any] = select().select_from(self.table)
        for name in self.column_names:
            if name not in self.table.__table__.columns:
                raise ValueError(f"No column {name} in {self.table}")
            stmt = stmt.add_columns(self.table.__table__.columns[name])
        if self.params.search_value:
            stmt = self._add_global_search_criterion(stmt)
        stmt = self._add_column_search_criteria(stmt)
        stmt = self._add_order_criteria(stmt)
        stmt = self._add_search_builder_criteria(stmt)
        return stmt

    async def _get_data(
        self, session: AsyncSession, stmt: Select[Any]
    ) -> list[dict[str, Any]]:
        """Get the data from the database"""
        # adding pagination by page (offset/start) and page size (limit/length)
        stmt = stmt.offset(self.params.start).limit(self.params.length)
        result: Result[Any] = await session.execute(stmt)
        # create a dictionary that maps the result of the query to a list
        data: list[dict[str, Any]] = []
        for row in result.all():
            result_row: dict[str, Any] = dict(zip(self.column_names, row, strict=True))
            if self.callbacks:
                self.callbacks.run(result_row)
            data.append(result_row)
        return data

    async def _run(self) -> None:
        # get total record count from the table
        self.records_total = await self._get_records_total()
        # get the select statement with all the search and order criteria
        stmt: Select[Any] = self._built_select_statement()
        # get the filtered record count from the statement that will also produce the data
        self.records_filtered = await self._get_records_filtered(stmt.subquery())
        # get the filtered records from the database
        self.data = await self._get_data(self.session, stmt)

    async def execute(
        self, request_params: dict[str, Any], callbacks: DTDataCallbacks | None = None
    ) -> dict[str, Any]:
        try:
            self.callbacks = callbacks
            self.params = self._parse_params(request_params)
            self.search_builder = self._parse_search_builder_params(request_params)
            self.column_names = [
                col.data for col in self.params.columns if col.data != ""
            ]
        except Exception as exc:
            self.error = str(exc)

        await self._run()

        result: dict[str, Any] = {
            "start": self.params.start,
            "length": self.params.length,
            "draw": self.params.draw,
            "recordsTotal": self.records_total,
            "recordsFiltered": self.records_filtered,
            "data": self.data if self.data else [],
        }
        if self.error:
            result["error"] = self.error
        return result
