from datetime import datetime

from sqlalchemy import Column, and_, or_
from sqlalchemy.sql.expression import BinaryExpression

from scraper_service.datatables.base import Condition


def build_criteria(table, criteria, logic: str = "AND"):
    """
    Recursively builds SQLAlchemy filters from parsed criteria.
    """
    expressions = []

    for item in criteria:
        if "criteria" in item:
            nested_expr = build_criteria(
                table, item["criteria"], item.get("logic", "AND")
            )
            expressions.append(nested_expr)
        else:
            expressions.append(_build_condition(table, item))

    return and_(*expressions) if logic == "AND" else or_(*expressions)


def _build_condition(table, condition_dict: Condition) -> BinaryExpression:
    """
    Converts a single condition dict to a SQLAlchemy condition with type validation.
    """
    column_name = condition_dict["origData"]
    column: Column = getattr(table.c, column_name)
    condition = condition_dict["condition"]
    value = condition_dict.get("value")
    column_type = condition_dict.get(
        "type", "string"
    )  # Default to string if type is not provided

    # Ensure value is a list
    if not isinstance(value, list):
        value = [value]

    # Define condition maps for each type
    string_conditions = {
        "=": lambda: column == value[0],
        "!=": lambda: column != value[0],
        "starts": lambda: column.like(f"{value[0]}%"),
        "!starts": lambda: column.notlike(f"{value[0]}%"),
        "contains": lambda: column.like(f"%{value[0]}%"),
        "!contains": lambda: column.notlike(f"%{value[0]}%"),
        "ends": lambda: column.like(f"%{value[0]}"),
        "!ends": lambda: column.notlike(f"%{value[0]}"),
        "null": lambda: column.is_(None),
        "!null": lambda: column.isnot(None),
    }

    num_conditions = {
        "=": lambda: column == value[0],
        "!=": lambda: column != value[0],
        "<": lambda: column < value[0],
        "<=": lambda: column <= value[0],
        ">": lambda: column > value[0],
        ">=": lambda: column >= value[0],
        "between": lambda: column.between(
            value[0], value[1] if len(value) > 1 else value[0]
        ),
        "!between": lambda: ~column.between(
            value[0], value[1] if len(value) > 1 else value[0]
        ),
        "null": lambda: column.is_(None),
        "!null": lambda: column.isnot(None),
    }

    date_conditions = {
        "=": lambda: column == datetime.fromisoformat(value[0]),
        "!=": lambda: column != datetime.fromisoformat(value[0]),
        "<": lambda: column < datetime.fromisoformat(value[0]),
        "<=": lambda: column <= datetime.fromisoformat(value[0]),
        ">": lambda: column > datetime.fromisoformat(value[0]),
        ">=": lambda: column >= datetime.fromisoformat(value[0]),
        "between": lambda: column.between(
            datetime.fromisoformat(value[0]),
            datetime.fromisoformat(value[1])
            if len(value) > 1
            else datetime.fromisoformat(value[0]),
        ),
        "!between": lambda: ~column.between(
            datetime.fromisoformat(value[0]),
            datetime.fromisoformat(value[1])
            if len(value) > 1
            else datetime.fromisoformat(value[0]),
        ),
        "null": lambda: column.is_(None),
        "!null": lambda: column.isnot(None),
    }

    array_conditions = {
        "contains": lambda: column.any(value[0]),
        "without": lambda: ~column.any(value[0]),
        "=": lambda: column == value[0],
        "!=": lambda: column != value[0],
        "null": lambda: column.is_(None),
        "!null": lambda: column.isnot(None),
    }

    # Map column types to their respective condition maps
    condition_map_by_type = {
        "string": string_conditions,
        "html": string_conditions,
        "num": num_conditions,
        "num-fmt": num_conditions,
        "html-num": num_conditions,
        "html-num-fmt": num_conditions,
        "date": date_conditions,
        "moment": date_conditions,
        "luxon": date_conditions,
        "array": array_conditions,
    }

    # Get the appropriate condition map for the column type
    condition_map = condition_map_by_type.get(column_type)
    if not condition_map:
        raise ValueError(f"Unsupported column type: {column_type}")

    # Validate and apply the condition
    try:
        return condition_map[condition]()
    except KeyError:
        raise ValueError(f"Unsupported condition: {condition} for type: {column_type}")
