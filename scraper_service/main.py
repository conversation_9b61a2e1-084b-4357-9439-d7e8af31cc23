import logging
from contextlib import asynccontextmanager

import sentry_sdk
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from elasticapm.contrib.starlette import ElasticAPM, make_apm_client
from fastapi import APIRouter, Depends, FastAPI, HTTPException, Security, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import API<PERSON>eyHeader, HTTPAuthorizationCredentials, HTTPBearer
from sentry_sdk.integrations.logging import ignore_logger
from sqlalchemy.ext.asyncio import async_sessionmaker
from starlette.requests import Request
from starlette.responses import JSONResponse
from starlette.status import HTTP_403_FORBIDDEN

from scraper_service.api.api_tags import ApiTag, tags_metadata
from scraper_service.api.auth import TokenValidationError, validate_token
from scraper_service.api.dependencies import (
    ConfigDependency,
    get_async_db_engine,
    get_config,
)
from scraper_service.api.routes import (
    healthcheck,
    login_state,
    scraper_event_routes,
    scraper_operation_history_routes,
    scraper_routes,
    scraper_state_routes,
    traces,
)
from scraper_service.config import Config
from scraper_service.logic.alert_sender import AlertSender
from scraper_service.logic.exception import DomainBaseException
from scraper_service.logs import configure_logger

configure_logger()


def before_send_transaction(event, hint):
    is_healthcheck_transaction = event.get(
        "type"
    ) == "transaction" and "/health" in event.get("transaction")
    if is_healthcheck_transaction:
        return None
    return event


sentry_sdk.init(
    traces_sample_rate=1.0,
    before_send_transaction=before_send_transaction,
)

ignore_logger(
    "binaries_output_logger"
)  # do not treat logs from binaries_output_logger as errors


class Unauthorized(HTTPException):
    def __init__(self, detail: str = "Not authenticated") -> None:
        super().__init__(status_code=HTTP_403_FORBIDDEN, detail=detail)


def require_jwt(
    config: ConfigDependency,
    auth_token: HTTPAuthorizationCredentials | None = Security(
        HTTPBearer(auto_error=False)
    ),
):
    if auth_token is None:
        raise Unauthorized(detail="Missing JWT")
    try:
        return validate_token(
            auth_token.credentials, config.user_access_token_public_key
        )
    except TokenValidationError as e:
        raise Unauthorized(detail=str(e))


def require_api_key(
    config: ConfigDependency,
    api_key_header: str = Security(APIKeyHeader(name="x-api-key", auto_error=False)),
):
    if api_key_header is not None:
        if api_key_header != config.api_key:
            raise Unauthorized(detail="Invalid API key")
        else:
            return
    raise Unauthorized("Not authenticated")


@asynccontextmanager
async def lifespan(app: FastAPI):
    config = get_config()
    async_db_engine = await get_async_db_engine(config)
    session_maker = async_sessionmaker(async_db_engine, expire_on_commit=False)
    message_scheduler = AlertSender(config=config, sessionmaker=session_maker)

    scheduler = AsyncIOScheduler()

    scheduler.add_job(
        message_scheduler.run_checks,
        CronTrigger(minute=0, second=0, timezone="UTC"),
    )
    scheduler.add_job(
        message_scheduler.reset_last_sent_message,
        CronTrigger(hour=7, minute=30, second=0, timezone="UTC"),
    )
    scheduler.start()
    yield

    scheduler.shutdown()


app = FastAPI(
    title="Scraper Service",
    openapi_tags=tags_metadata,
    lifespan=lifespan,
)

# Use this router if your endpoint should be accessible without any authorization
main_public_router = APIRouter(tags=[ApiTag.PUBLIC])

# Use this router if your endpoint should be accessible by other services or admin using API key
main_internal_router = APIRouter(
    dependencies=[Depends(require_api_key)], tags=[ApiTag.INTERNAL]
)

# Use this router if your endpoint should be accessible by users using JWT token
main_external_router = APIRouter(
    dependencies=[Depends(require_jwt)],
    tags=[ApiTag.EXTERNAL],
    prefix="/external",
)


@app.exception_handler(DomainBaseException)
def handle_base_exception(request: Request, exc: DomainBaseException):
    return JSONResponse(
        status_code=500,
        content={"message": "Unexpected domain error occurred", "details": exc.details},
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logging.error(f"{request}: {exc}")
    content = {
        "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
        "message": f"{exc}",
        "data": None,
    }
    return JSONResponse(
        content=content, status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
    )


def run_elastic_apm():
    config: Config = get_config()
    if config.elastic_secret_token:
        app.add_middleware(
            ElasticAPM,
            client=make_apm_client({
                "SERVICE_NAME": config.service_name,
                "SECRET_TOKEN": config.elastic_secret_token,
                "SERVER_URL": config.elastic_service_url,
                "ENVIRONMENT": config.env,
                "TRANSACTION_IGNORE_URLS": [
                    config.healthcheck_url,
                    f"{config.healthcheck_url}/*",
                ],
                "PROCESSORS": [
                    # TODO enable to collect app stats
                    # "scraper_service.tracing.elastic_tracer",  # noqa: ERA001
                    "elasticapm.processors.sanitize_stacktrace_locals",
                    "elasticapm.processors.sanitize_http_request_cookies",
                    "elasticapm.processors.sanitize_http_headers",
                    "elasticapm.processors.sanitize_http_wsgi_env",
                    "elasticapm.processors.sanitize_http_request_body",
                ],
            }),
        )


run_elastic_apm()


app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:8666",
        "http://127.0.0.1:8666",
    ],
    allow_methods=["*"],
    allow_headers=["*"],
)
main_public_router.include_router(healthcheck.public_router)

main_internal_router.include_router(scraper_operation_history_routes.internal_router)
main_internal_router.include_router(scraper_state_routes.internal_router)
main_internal_router.include_router(scraper_routes.internal_router)
main_internal_router.include_router(login_state.internal_router)

main_external_router.include_router(scraper_state_routes.external_router)
main_external_router.include_router(scraper_event_routes.external_router)
main_external_router.include_router(traces.external_router)

app.include_router(main_public_router)
app.include_router(main_external_router)
app.include_router(main_internal_router)
