from typing import Annotated

from pydantic import AfterValidator
from pydantic_settings import BaseSettings, SettingsConfigDict

from scraper_service.logic.entities import DatabaseURL, OriginID


def async_db_migration(value: DatabaseURL) -> DatabaseURL:
    """
    Migrate the database URL from psycopg2 to psycopg3.
    # TODO: remove after updating database url in single click
    """
    psycopg3_driver = "postgresql+psycopg://"
    if "postgresql://" in value:
        return DatabaseURL(value.replace("postgresql://", psycopg3_driver))
    elif "postgresql+psycopg2://" in value:
        return DatabaseURL(value.replace("postgresql+psycopg2://", psycopg3_driver))
    elif psycopg3_driver in value:
        return value
    raise ValueError("Database URL must be a PostgreSQL URL")


ValidatedDatabaseURL = Annotated[DatabaseURL, AfterValidator(async_db_migration)]


class Config(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    hostname: str = "localhost"
    env: str = "local"
    app_version: str = "1.0.0"
    service_name: str = "scraper-service"
    docker_tag: str = ""
    docker_build_timestamp: str = ""

    healthcheck_url: str = "/health"

    elastic_secret_token: str | None = None
    elastic_service_url: str = "elastic-service-url"

    user_access_token_public_key: str = (
        ""  # should be required, but without it tests fail
    )
    api_key: str | None = None

    user_service_url: str | None = None
    user_service_key: str | None = None

    database_url: ValidatedDatabaseURL
    database_pool_size: int = 20
    teams_webhook_url: str | None = None

    @property
    def origin(self) -> OriginID:
        return f"{self.service_name}-{self.app_version}-{self.docker_tag}"

    @property
    def user_agent(self) -> OriginID:
        return f"IndieBI {self.service_name}-{self.app_version}"
