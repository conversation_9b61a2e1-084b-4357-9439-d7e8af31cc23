import httpx


class TeamsWebhookException(Exception):
    pass


async def send_to_channel(webhook_url, message):
    headers = {"Content-Type": "application/json"}
    payload = {"text": message}
    async with httpx.AsyncClient() as client:
        response = await client.post(
            webhook_url, json=payload, headers=headers, timeout=60
        )
    if response.status_code != 200:
        raise TeamsWebhookException(
            f"Request to Teams webhook failed with status {response.status_code}"
        )
