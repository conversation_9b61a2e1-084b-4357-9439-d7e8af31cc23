import logging
from datetime import datetime

from azure.identity import DefaultAzureCredential
from sqlalchemy import DateTime, event
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column

logger: logging.Logger = logging.getLogger(__name__)


class Base(DeclarativeBase):
    pass


class DatabaseBaseModel(Base):
    __abstract__ = True

    id: Mapped[int] = mapped_column(primary_key=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))


def setup_token_refresh(db_engine):
    @event.listens_for(db_engine, "do_connect")
    def provide_token(dialect, conn_rec, cargs, cparams):
        logger.info("Refreshing Azure token for DB connection")
        credential = DefaultAzureCredential(
            logging_enable=True,
            exclude_shared_token_cache_credential=True,
        )
        token = credential.get_token(
            "https://ossrdbms-aad.database.windows.net/.default"
        ).token
        cparams["password"] = token
