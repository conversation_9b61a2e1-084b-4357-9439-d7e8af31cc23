import requests

from scraper_service.config import Config
from scraper_service.logic.entities import Organization, OrganizationID, User, UserID

DEFAULT_TIMEOUT = 5


class UserServiceClient:
    def __init__(self, config: Config) -> None:
        self._config: Config = config

    def get_organization_by_id(self, org_id: OrganizationID) -> Organization:
        response = requests.get(
            f"{self._config.user_service_url}/organization/{org_id}",
            headers={
                "x-api-key": self._config.user_service_key,
                "User-Agent": self._config.user_agent,
            },
            timeout=DEFAULT_TIMEOUT,
        )
        response.raise_for_status()
        return Organization.model_validate(response.json())

    def get_user_by_id(self, user_id: UserID) -> User:
        response = requests.get(
            f"{self._config.user_service_url}/user/{user_id}",
            headers={
                "x-api-key": self._config.user_service_key,
                "User-Agent": self._config.user_agent,
            },
            timeout=DEFAULT_TIMEOUT,
        )
        response.raise_for_status()
        return User.model_validate(response.json())
