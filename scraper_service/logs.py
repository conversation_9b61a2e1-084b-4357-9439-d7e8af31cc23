import collections
import logging
import logging.config
import os
import sys
import threading
from contextlib import contextmanager
from copy import deepcopy

import ecs_logging

log = logging.getLogger(__name__)


_THREAD_LOCAL = threading.local()
_THREAD_LOCAL.pids = collections.defaultdict(dict)


@contextmanager
def add_logging_context(**kwargs):
    new_extra = kwargs
    old_extra = _get_extra()
    _set_extra({**old_extra, **new_extra})
    yield
    _set_extra(old_extra)


def _set_extra(extra):
    _init_extra()
    pid = os.getpid()
    _THREAD_LOCAL.pids[pid] = extra
    return _THREAD_LOCAL.pids[pid]


def _except_logging(exc_type, exc_value, exc_traceback):
    log.exception(
        f"Exception hook. Type: {exc_type}, stacktrace: {exc_traceback}",
        exc_info=exc_value,
    )


def _unraisable_logging(
    exc_type, exc_value=None, exc_traceback=None, err_msg=None, obj=None
):
    log.exception(
        f"Unraisable hook. Type: {exc_type}, err_msg: {err_msg}, object: {obj}, stacktrace: {exc_traceback}",
        exc_info=exc_value,
    )


def _threading_except_logging(
    exc_type, exc_value=None, exc_traceback=None, thread=None
):
    log.exception(
        f"Threading exception hook. Type: {exc_type}, thread: {thread}, stacktrace: {exc_traceback}",
        exc_info=exc_value,
    )


def _init_extra():
    if not hasattr(_THREAD_LOCAL, "pids"):
        _THREAD_LOCAL.pids = collections.defaultdict(dict)


def _get_extra():
    _init_extra()
    pid = os.getpid()
    return _THREAD_LOCAL.pids[pid]


def get_logging_context():
    return deepcopy(_get_extra())


class ColoredFormatter(logging.Formatter):
    LEVEL_COLORS = {
        "DEBUG": "\033[1;36m",  # cyan, bold
        "INFO": "\033[0m",  # default
        "WARNING": "\033[1;33m",  # yellow, bold
        "ERROR": "\033[1;31m",  # red, bold
        "CRITICAL": "\033[1;41m",  # red background, bold
    }
    LIGHT_BLUE = "\033[0;94m"
    PURPLE = "\033[0;95m"
    BOLD = "\033[1m"
    RESET = "\033[0m"

    def helper(self, record: logging.LogRecord, new_format: str):
        # pylint: disable=protected-access
        original_format = self._style._fmt
        self._style._fmt = new_format
        result = super().format(record)
        self._style._fmt = original_format
        # pylint: enable=protected-access
        return result


class ColoredSimplestFormatter(ColoredFormatter):
    def format(self, record: logging.LogRecord):
        return super().helper(
            record,
            f"{self.LEVEL_COLORS.get(record.levelname, "")}%(message)s{self.RESET}",
        )


class ColoredShortFormatter(ColoredFormatter):
    def format(self, record: logging.LogRecord):
        return super().helper(
            record,
            f"{self.LIGHT_BLUE}%(asctime)s.%(msecs)03d{self.RESET} | "
            + f"{self.LEVEL_COLORS.get(record.levelname, "")}%(message)s{self.RESET}",
        )


class ColoredVerboseFormatter(ColoredFormatter):
    def format(self, record: logging.LogRecord):
        new_format = (
            f"{self.LIGHT_BLUE}%(asctime)s.%(msecs)03d{self.RESET} - "
            + f"{self.PURPLE}%(name)s{self.RESET} - "
            + f"{self.LEVEL_COLORS.get(record.levelname, "")}{record.levelname} | "
            + f"%(message)s {self.RESET}"
        )
        return super().helper(record, new_format)


class ContextFilter(logging.Filter):
    def filter(self, record):
        context = _get_extra()
        for key, value in context.items():
            setattr(record, key, value)
        return True


def configure_logger():
    logging_config = {
        "version": 1,
        # "formatters": {"ecs": {"()": ColoredVerboseFormatter}},  # noqa: ERA001
        "formatters": {"ecs": {"()": ecs_logging.StdlibFormatter}},  # noqa: ERA001
        "filters": {
            "context": {
                "()": "scraper_service.logs.ContextFilter",
            }
        },
        "handlers": {
            "standard_output": {
                "class": "logging.StreamHandler",
                "formatter": "ecs",
                "stream": "ext://sys.stdout",
                "filters": ["context"],
            },
        },
        "loggers": {
            "root": {"level": "INFO", "handlers": ["standard_output"]},
            "binaries_output_logger": {
                "level": "INFO",
                "handlers": ["standard_output"],
                "propagate": False,
            },
            "uvicorn": {"propagate": True},
        },
    }

    logging.config.dictConfig(logging_config)

    sys.excepthook = _except_logging
    sys.unraisablehook = _unraisable_logging
    threading.excepthook = _threading_except_logging
