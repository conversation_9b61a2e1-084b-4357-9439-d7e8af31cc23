from datetime import UTC, datetime, timedelta
from enum import StrEnum
from typing import Any

from pydantic import BaseModel, ConfigD<PERSON>, GetCoreSchemaHandler
from pydantic_core import CoreSchema, core_schema


class BaseStrField(str):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: GetCoreSchemaHandler
    ) -> CoreSchema:
        return core_schema.no_info_after_validator_function(cls, handler(str))


class DatabaseURL(BaseStrField): ...


class LoginState(StrEnum):
    SCHEDULED = "SCHEDULED"
    STARTED = "STARTED"
    STOPPED = "STOPPED"
    CONFIGURED = "CONFIGURED"
    FAILED = "FAILED"
    UNCONFIGURED = "UNCONFIGURED"


class TriggeredBy(StrEnum):
    SCHEDULE = "SCHEDULE"
    SHADOW_TASK = "SHADOW_TASK"
    USER_VIA_ELECTRON = "USER_VIA_ELECTRON"
    DATA_PIPELINE = "DATA_PIPELINE"


class ScraperBinaryStatus(StrEnum):
    # scraped was configured (have working session after login)
    CONFIGURED = "CONFIGURED"
    # scraper was scheduled to run
    SCHEDULED = "SCHEDULED"
    # scraper was started (manually or automatically)
    STARTED = "STARTED"
    # scraper was stopped (manually)
    STOPPED = "STOPPED"
    # scraper finished with success
    FINISHED = "FINISHED"
    # scraper failed with some error
    FAILED = "FAILED"
    UNCONFIGURED = "UNCONFIGURED"
    DELETED = "DELETED"
    # scraper was "disabled" in electron app, meaning it shouldn't run until enabled again
    DISABLED = "DISABLED"

    # scraper was blocked by IndieBI user
    MANUALLY_BLOCKED = "MANUALLY_BLOCKED"
    MANUALLY_UNBLOCKED = "MANUALLY_UNBLOCKED"


OrganizationID = str
UserID = str
OriginID = str
OperationID = str


class ExpiringBase(BaseModel):
    updated_at: datetime | None = None

    def is_expired(self, ttl: timedelta) -> bool:
        return self.updated_at is None or self.updated_at > datetime.now(UTC) - ttl


class Organization(ExpiringBase):
    id: OrganizationID
    name: str


class User(ExpiringBase):
    id: UserID
    email: str


class OrganizationFeatureFlags(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: OrganizationID
    feature_flags: list[str]


class UserFeatureFlags(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UserID
    feature_flags: list[str]


# We do not want to specify here all possible sources for two reasons:
# 1. S2 is and should stay source-agnostic. We want to be able to add new sources
# without changing the S2 code all the time. S2 does not have any source-specific logic,
# so this is not a problem.
#
# 2. S2, via the ReportUploaded event, gets information about two kinds of sources.
# One is scraper-based, the other is report-based. Instead of maintaining two different
# types for sources, we can simply treat them as black boxes.
# Check this for more details:
# https://gitlab.com/bluebrick/indiebi/data-platform-team/scraper-api/-/blob/main/src/utils/Source.ts?ref_type=heads#L43


Source = str


class DateRange(BaseModel):
    date_from: str
    date_to: str
    days_in_range: int
