from typing import Literal

from pydantic import BaseModel

from scraper_service.logic.entities import Source
from scraper_service.logic.events.event import Event
from scraper_service.logic.events.remote_client_event_body import RemoteClientEventBody


class ScrapeResult(BaseModel):
    report_file_name: str
    start_date: str
    end_date: str
    source: Source
    no_data: bool


class ReportInfo(BaseModel):
    studio_id: int
    original_name: str
    upload_date: str
    file_path_raw: str
    date_from: str
    date_to: str
    source: Source
    no_data: bool
    state: str
    id: int


class ReportUploadEventBody(RemoteClientEventBody):
    source: Source
    report_info: ReportInfo
    scrape_result: ScrapeResult


class ReportUploadedEvent(Event):
    event_type: Literal["report_uploaded"] = "report_uploaded"
    body: ReportUploadEventBody
