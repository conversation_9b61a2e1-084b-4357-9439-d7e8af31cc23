from typing import Literal

from pydantic import BaseModel

from scraper_service.logic.entities import ScraperBinaryStatus, Source
from scraper_service.logic.events.event import Event


class ScraperBlockadeChangedBody(BaseModel):
    new_state: Literal[
        ScraperBinaryStatus.MANUALLY_BLOCKED, ScraperBinaryStatus.MANUALLY_UNBLOCKED
    ]

    source: Source


class ScraperBlockadeChangedEvent(Event):
    event_type: Literal["scraper_blockade_changed"] = "scraper_blockade_changed"
    body: ScraperBlockadeChangedBody
