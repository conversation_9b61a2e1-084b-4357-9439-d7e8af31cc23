from typing import Literal

from scraper_service.logic.entities import LoginState
from scraper_service.logic.events.event import Event
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedBody


class LoginStateChangedBody(ScraperStateChangedBody):
    new_state: LoginState
    is_manual_session: bool = False


class LoginStateChangedEvent(Event):
    event_type: Literal["login_state_changed"] = "login_state_changed"
    body: LoginStateChangedBody
