from typing import Literal

from scraper_service.logic.entities import (
    Date<PERSON><PERSON><PERSON>,
    ScraperBinaryStatus,
    Source,
    TriggeredBy,
)
from scraper_service.logic.events.event import Event
from scraper_service.logic.events.remote_client_event_body import RemoteClientEventBody


class ScraperStateChangedBody(RemoteClientEventBody):
    new_state: ScraperBinaryStatus
    """
    New scraper state.
    """

    source: Source
    """
    Source (eg. steam_sales, nintendo_discounts, etc) that this event fired for.
    """

    account_identifier: str
    """
    Found in scraper configurations.
    """

    reason: str | None = None
    """
    Reason why scraper failed or changed it's state. Currently used to determine failure reason.
    """

    triggered_by: TriggeredBy | None = None

    date_ranges: list[DateRange] | None = None


class ScraperStateChangedEvent(Event):
    event_type: Literal["scraper_state_changed"] = "scraper_state_changed"
    body: ScraperStateChangedBody
