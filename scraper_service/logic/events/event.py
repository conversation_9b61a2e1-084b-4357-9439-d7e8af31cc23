from datetime import UTC, datetime
from typing import Any

from pydantic import BaseModel, Field, field_validator
from sqlalchemy import DateTime, String
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import Base
from scraper_service.logic.entities import (
    OrganizationID,
    OriginID,
    UserID,
)


class EventBody(BaseModel):
    pass


class Event(BaseModel, validate_assignment=True):
    """
    Event is a class that represents any incoming event that S2 can handle.
    Events are then applied to projections to update their state.
    Only fields that are common to all events should be defined here.
    More specific events should inherit from this class and add their own fields.
    """

    event_type: Any
    """
    event_type is a discriminator field that is used to determine the type of the event.
    it is used by Pydantic to choose which class to instantiate when deserializing JSON.
    subclasses should implement this as:
     event_type: Literal["my_event"] = "my_event"
    """

    received_at: datetime = datetime.now(tz=UTC)
    """
    received_at is the time when the event was received. Uses server time in UTC.
    """

    client_timestamp: datetime
    """
    client_timestamp is the local client time when the event was created.
    Can contain local client timezone information.
    """

    user_id: UserID = Field("not-provided", validate_default=False)
    """
    IndieBI user id (e.g. "u-xABc") that triggered the event.
    """

    organization_id: OrganizationID = Field("not-provided", validate_default=False)
    """
    IndieBI organization id (e.g. "o-xABc") for which the event was triggered.
    """

    origin: OriginID

    """
    Name of the application/library that triggered the event (scraper-lib-1.10.0, scraper-js-0.200.0, scraper-py-0.1.0).
    """

    body: EventBody | None = None
    """
    Additional details about the event.
    """

    # TODO this should be moved to specific body classes.
    operation_id: str | None = None
    """
    operation_id is a unique identifier for the run of a scraper.
    """

    # TODO exclude this from the API endpoint and populate it internally
    @field_validator("received_at")
    @classmethod
    def validate_received_at(cls, value: datetime):
        if value.tzinfo != UTC:
            raise ValueError("received_at must be in UTC timezone")
        return value


class _EventModel(Base):
    __tablename__ = "event"

    id: Mapped[int] = mapped_column(primary_key=True)
    event_type: Mapped[str] = mapped_column(String(100))
    received_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    client_timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    user_id: Mapped[str] = mapped_column(String(100))
    organization_id: Mapped[str] = mapped_column(String(100))
    origin: Mapped[str]
    body: Mapped[dict | None] = mapped_column(JSONB)
    operation_id: Mapped[str | None] = mapped_column(String(100))

    def __repr__(self) -> str:
        return (
            f"Event(id={self.id!r}, event_type={self.event_type!r}, received_at={self.received_at!r}, "
            f"client_timestamp={self.client_timestamp!r}, user_id={self.user_id!r}, "
            f"organization_id={self.organization_id!r}, origin={self.origin!r}, "
            f"body={self.body!r}, operation_id={self.operation_id!r})"
        )


class EventsRepository:
    def __init__(self, session: AsyncSession) -> None:
        self._session: AsyncSession = session

    async def add(self, event: Event) -> None:
        new_event = _EventModel(**event.model_dump())
        self._session.add(new_event)
        await self._session.flush()
