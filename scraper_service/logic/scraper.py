from collections import defaultdict
from datetime import UTC, datetime
from functools import singledispatchmethod

from pydantic import BaseModel

from scraper_service.logic.entities import (
    OrganizationID,
    OriginID,
    ScraperBinaryStatus,
    Source,
    UserID,
)
from scraper_service.logic.events.event import Event, EventsRepository
from scraper_service.logic.events.scraper_blockade_change import (
    ScraperBlockadeChangedBody,
    ScraperBlockadeChangedEvent,
)
from scraper_service.logic.exception import CommandNotSupported, ScraperNotConfigured
from scraper_service.logic.models.scraper_status import ScraperStatus
from scraper_service.logic.projections.base import Projection
from scraper_service.logic.repositories.scraper_status import ScraperStatusRepository


class Command(BaseModel):
    pass


class HandleEventCommand(Command):
    event: Event
    current_user: dict | None


class BlockCommand(Command):
    organization_id: OrganizationID
    user_id: UserID
    source: Source


class UnblockCommand(Command):
    organization_id: OrganizationID
    user_id: UserID
    source: Source


class ScraperApplication:
    def __init__(
        self,
        origin: OriginID,
        events_repository: EventsRepository,
        scraper_status_repository: ScraperStatusRepository,
    ) -> None:
        self._origin: OriginID = origin
        self._events_repository: EventsRepository = events_repository
        self._scraper_status_repository: ScraperStatusRepository = (
            scraper_status_repository
        )
        self._handlers: dict[type[Event], list[Projection]] = defaultdict(list)

    def register(self, projection: Projection, *events: type[Event]) -> None:
        for e in events:
            self._handlers[e].append(projection)

    def _projections_for(self, event: Event) -> list[Projection]:
        return self._handlers[type(event)]

    async def _apply_to_projections(self, event: Event):
        for projection in self._projections_for(event):
            await projection.handle(event)

    @singledispatchmethod
    async def handle(self, command: Command) -> None:
        raise NotImplementedError

    @handle.register
    async def _(self, command: HandleEventCommand) -> None:
        event = command.event
        current_user = command.current_user

        # TODO: remove if not needed?
        event.received_at = datetime.now(UTC)

        organization_id = (
            current_user["main_organization_id"]
            if current_user
            else event.organization_id
        )
        user_id = current_user["user_id"] if current_user else event.user_id
        event.organization_id = organization_id
        event.user_id = user_id

        await self._events_repository.add(event=event)

        await self._apply_to_projections(event)

    @handle.register
    async def _(self, command: BlockCommand | UnblockCommand) -> None:
        current_status: ScraperStatus = await self._scraper_status_repository.get(
            organization_id=command.organization_id,
            user_id=command.user_id,
            source=command.source,
        )
        if current_status.state == ScraperBinaryStatus.UNCONFIGURED:
            raise ScraperNotConfigured

        match command:
            case BlockCommand():
                new_state = ScraperBinaryStatus.MANUALLY_BLOCKED
            case UnblockCommand():
                new_state = ScraperBinaryStatus.MANUALLY_UNBLOCKED
            case _:
                raise CommandNotSupported

        event = ScraperBlockadeChangedEvent(
            client_timestamp=datetime.now(tz=UTC),
            organization_id=command.organization_id,
            user_id=command.user_id,
            origin=self._origin,
            body=ScraperBlockadeChangedBody(
                source=command.source,
                new_state=new_state,
            ),
        )

        await self._events_repository.add(event=event)

        await self._apply_to_projections(event)
