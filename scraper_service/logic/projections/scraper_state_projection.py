from datetime import datetime
from typing import Any

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, Integer, Select, String, select
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import DatabaseBaseModel
from scraper_service.datatables import DataTable
from scraper_service.logic.entities import OrganizationID, Source, UserID
from scraper_service.logic.models.scraper_status import ProjectionScraperStatus


class _ScraperStatusProjectionModel(DatabaseBaseModel):
    __tablename__ = "scraper_status_projection"
    organization_id: Mapped[str] = mapped_column(String(100))
    user_id: Mapped[str] = mapped_column(String(100))
    user_email: Mapped[str] = mapped_column(String(100))
    organization_name: Mapped[str] = mapped_column(String(100))
    source: Mapped[str] = mapped_column(String(100))
    state: Mapped[str] = mapped_column(String(100))
    uses_manual_session: Mapped[bool | None] = mapped_column(<PERSON><PERSON><PERSON>)
    triggered_by: Mapped[str | None] = mapped_column(String(100))
    consecutive_failed_scrape_count: Mapped[int] = mapped_column(Integer, default=0)
    last_success_timestamp: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True)
    )
    last_fail_timestamp: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True)
    )
    last_fail_reason: Mapped[str | None] = mapped_column(String(100))
    last_command: Mapped[str | None] = mapped_column(String(100))
    last_origin: Mapped[str | None]
    version: Mapped[int] = mapped_column(Integer, default=0)
    last_operation_id: Mapped[str | None] = mapped_column(String(100))
    last_report_ids: Mapped[list[int] | None] = mapped_column(ARRAY(Integer))
    last_scrape_date_ranges: Mapped[list[dict] | None] = mapped_column(JSONB)


class ScraperStatusProjection:
    def __init__(
        self,
        session: AsyncSession,
    ) -> None:
        self._session: AsyncSession = session

    async def get_datatable_format(self, params: dict[str, Any]):
        datatable: DataTable = DataTable(
            session=self._session,
            table=_ScraperStatusProjectionModel,
        )
        results = await datatable.execute(request_params=params)
        return results

    async def get(
        self, organization_id: OrganizationID, user_id: UserID, source: Source
    ) -> ProjectionScraperStatus:
        stmt = select(
            _ScraperStatusProjectionModel,
        ).where(
            _ScraperStatusProjectionModel.organization_id == organization_id,
            _ScraperStatusProjectionModel.user_id == user_id,
            _ScraperStatusProjectionModel.source == source,
        )

        result = await self._session.execute(stmt)
        entity = result.scalar_one_or_none()

        if entity:
            return ProjectionScraperStatus.model_validate(entity)

        return ProjectionScraperStatus(
            organization_id=organization_id, user_id=user_id, source=source
        )

    async def get_all(
        self,
        organization_id: OrganizationID | None = None,
        organization_ids: list[OrganizationID] | None = None,
        source: Source | None = None,
        ignored_sources: list[Source] | None = None,
    ) -> list[ProjectionScraperStatus]:
        stmt: Select = select(_ScraperStatusProjectionModel)

        if organization_id is not None:
            stmt = stmt.where(
                _ScraperStatusProjectionModel.organization_id == organization_id
            )
        if organization_ids:
            stmt = stmt.where(
                _ScraperStatusProjectionModel.organization_id.in_(organization_ids)
            )
        if source is not None:
            stmt = stmt.where(_ScraperStatusProjectionModel.source == source)
        if ignored_sources:
            stmt = stmt.where(
                _ScraperStatusProjectionModel.source.notin_(ignored_sources)
            )

        result = await self._session.execute(stmt)
        entities = result.scalars().all()

        return [ProjectionScraperStatus.model_validate(entity) for entity in entities]
