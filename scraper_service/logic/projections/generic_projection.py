from collections.abc import Awaitable, Callable
from functools import singledispatchmethod
from typing import Any

from scraper_service.logic.events.event import Event
from scraper_service.logic.projections.base import Projection


class GenericProjection(Projection):
    """
    Generic projection that maps events to repository get functions.

    Usage:
        projection = GenericProjection(
            repository=login_status_repo,
            event_types=[LoginStateChangedEvent],
            get_entity=lambda repo, event: repo.get(
                organization_id=event.organization_id,
                user_id=event.user_id,
                source=event.body.source,
            )
        )
    """

    def __init__(
        self,
        repository: Any,
        event_types: list[type[Event]],
        get_entity: Callable[[Any, Event], Awaitable[Any]],
    ):
        self._repository = repository
        self._get_entity = get_entity
        self.event_types = event_types  # Store for auto-registration

        # Register handlers for all event types
        for event in event_types:
            self._register_handler(event)

    def _register_handler(self, event_type: type[Event]) -> None:
        """Register a handler for the given event type."""

        @self.handle.register
        async def _(self_inner, event: event_type) -> None:  # type: ignore[misc,valid-type]
            entity = await self_inner._get_entity(self_inner._repository, event)
            entity.apply(event)
            await self_inner._repository.save(entity)

    @singledispatchmethod
    async def handle(self, event: Event) -> None:
        """Base handle method - raises NotImplementedError for unregistered events."""
        raise NotImplementedError(
            f"No handler registered for event type: {type(event)}"
        )
