from datetime import timedelta
from functools import singledispatchmethod

from scraper_service.connectors.user_service_client import UserServiceClient
from scraper_service.logic.events.event import Event
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedEvent
from scraper_service.logic.projections.base import Projection
from scraper_service.logic.repositories.organization import OrganizationRepository

DEFAULT_TIME_TO_LIFE = timedelta(days=1)


class OrganizationAggregator(Projection):
    def __init__(
        self,
        repository: OrganizationRepository,
        user_service_client: UserServiceClient,
        ttl=DEFAULT_TIME_TO_LIFE,
    ):
        self._repository = repository
        self._user_service_client = user_service_client
        self.ttl = ttl

    @singledispatchmethod
    async def handle(self, event: Event) -> None:
        """Base handle method - raises NotImplementedError for unregistered events."""
        raise NotImplementedError(
            f"No handler registered for event type: {type(event)}"
        )

    @handle.register
    async def _(self, event: <PERSON>raperStateChangedEvent) -> None:  # type: ignore[misc,valid-type]
        organization_id = (
            event.organization_id
        )  # TODO optimize for many events with the same user_id, to don't spam US
        entity = await self._repository.get(organization_id)
        if entity is None or entity.is_expired(self.ttl):
            org = self._user_service_client.get_organization_by_id(organization_id)
            await self._repository.save(org)
