from datetime import timedelta
from functools import singledispatchmethod

from scraper_service.connectors.user_service_client import UserServiceClient
from scraper_service.logic.entities import User
from scraper_service.logic.events.event import Event
from scraper_service.logic.events.scraper_state_changed import ScraperState<PERSON>hangedEvent
from scraper_service.logic.projections.base import Projection
from scraper_service.logic.repositories.user import UserRepository

DEFAULT_TIME_TO_LIFE = timedelta(days=1)


class UserAggregator(Projection):
    def __init__(
        self,
        repository: UserRepository,
        user_service_client: UserServiceClient,
        ttl=DEFAULT_TIME_TO_LIFE,
    ):
        self._repository = repository
        self._user_service_client = user_service_client
        self.ttl = ttl

    @singledispatchmethod
    async def handle(self, event: Event) -> None:
        """Base handle method - raises NotImplementedError for unregistered events."""
        raise NotImplementedError(
            f"No handler registered for event type: {type(event)}"
        )

    @handle.register
    async def _(self, event: ScraperStateChangedEvent) -> None:  # type: ignore[misc,valid-type]
        user_id = (
            event.user_id
        )  # TODO optimize for many events with the same user_id, to don't spam US
        await self.get(user_id)

    async def get(self, user_id) -> User:
        entity = await self._repository.get(user_id)
        if entity is None or entity.is_expired(self.ttl):
            entity = self._user_service_client.get_user_by_id(user_id)
            await self._repository.save(entity)
        return entity
