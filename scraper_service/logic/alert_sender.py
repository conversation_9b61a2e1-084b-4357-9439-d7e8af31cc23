import json
import urllib.parse

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker

from scraper_service.config import Config
from scraper_service.connectors.ms_teams import send_to_channel
from scraper_service.logic.entities import OrganizationID
from scraper_service.logic.repositories.scraper_status import ScraperStatusRepository

on_vms_organizations = [
    "o-OBKuMq",  # 8-4 (Managed Partner)
    "o-EkcrcX",  # <PERSON><PERSON><PERSON> (Managed Partner)
    "o-rZww8b",  # <PERSON><PERSON><PERSON><PERSON> (Managed Partner)
    "o-ZlvDIv",  # Boneloaf (Managed Partner)
    "o-A6KRx1",  # Facepunch (Managed Partner)
    "o-0GKA0m",  # Freedom Games aka Indie.io
    "o-NsTbwo",  # Greenheart Games (Managed Partner)
    "o-wCHDu9",  # Hinterland Games (Managed Partner)
    "o-t4As39",  # I-Illusions (Managed Partner)
    "o-n7ByG0",  # Innersloth (Managed Partner)	2021-01-04 11:24:48
    "o-dqzcsB",  # <PERSON> (Managed Partner)
    "o-NzYTKL",  # Landfall (Managed Partner)
    "o-XT8fjD",  # Mega Crit (Managed Partner)
    "o-x9UnXJ",  # Monomi Park (Managed Partner)
    "o-Uq0AtF",  # Outersloth (Managed Client)
    "o-pxRmqd",  # Panic (Managed Partner)
    "o-Nzt0pF",  # Pine Studio (Managed Partner)
    "o-DvS1Ua",  # Pocketpair
    "o-LIYf78",  # Raw Fury
    "o-PAmMTX",  # Red Hook Studios (Managed Partner)
    "o-bQLihp",  # Relic Entertainment, Inc (Managed Partner)
    "o-xLhn1l",  # Schell Games (Managed Partner)
    "o-X1fjLv",  # Secret Mode (Managed Partner)
    "o-AL6Tv7",  # SUPERHOT (Managed Partner)
    "o-i1MFwj",  # SUPERHOT Presents
    "o-9mXgoO",  # Squanch (Managed Partner)
    "o-X3lIy7",  # The Mix Games (Managed Partner)
    "o-mD9z7q",  # Twin Sails (Managed Partner)
    "o-Tl0NfV",  # Worm Club (Managed Partner) aka Frog
    "o-NzYTKL",  # Landfall (Managed Partner)
    "o-zn9i0i",  # Revolution (Managed Partner)
    "o-U3vBSY",  # Game Mill (Managed Partner)
]

EXPLANATION = """
🔺- new
✅ - resolved"""


class AlertSender:
    def __init__(
        self, config: Config, sessionmaker: async_sessionmaker[AsyncSession]
    ) -> None:
        self.teams_webhook_url = config.teams_webhook_url
        self.sessionmaker: async_sessionmaker[AsyncSession] = sessionmaker

        self.previous_statuses: set = set()
        self.previous_sources: set = set()
        self.data_office_url = (
            "https://dataoffice.indiebi.com"
            if config.env == "prod"
            else "https://dataoffice.indiebi.dev"
        )

    async def run_checks(self) -> None:
        if not self.teams_webhook_url:
            return
        async with self.sessionmaker() as session:
            scraper_statuses: ScraperStatusRepository = ScraperStatusRepository(session)
            await self.send_vm_scraped_organization_alerts(scraper_statuses)
            await self.send_global_issue_alerts(scraper_statuses)

    async def send_vm_scraped_organization_alerts(
        self, scraper_statuses: ScraperStatusRepository
    ) -> None:
        statuses = await scraper_statuses.get_statuses_needing_attention(
            organization_ids=on_vms_organizations, ignored_sources=["epic_sales"]
        )

        org_statuses: dict[OrganizationID, dict] = {}
        for status in statuses:
            if status.organization_id not in org_statuses:
                org_statuses[status.organization_id] = {
                    "sources": [],
                    "max_updated": status.updated_at,
                }
            org_statuses[status.organization_id]["sources"].append(status.source)
            if status.updated_at > org_statuses[status.organization_id]["max_updated"]:
                org_statuses[status.organization_id]["max_updated"] = status.updated_at

        current_statuses = {
            (org_id, source)
            for org_id, data in org_statuses.items()
            for source in data["sources"]
        }

        statuses_changed = current_statuses != self.previous_statuses
        statuses_message = ""
        if statuses and statuses_changed:
            statuses_message += (
                "| Organization | Failed Sources | Last Updated | Link |\n"
            )
            statuses_message += (
                "|--------------|----------------|--------------|------|"
            )

            sorted_orgs = sorted(
                org_statuses.items(), key=lambda x: x[1]["max_updated"], reverse=True
            )

            for org_id, data in sorted_orgs:
                sources_text = []
                for source in sorted(data["sources"]):
                    if (org_id, source) not in self.previous_statuses:
                        sources_text.append(f"🔺 {source}")
                    else:
                        sources_text.append(source)

                resolved_sources = [
                    source
                    for org, source in self.previous_statuses
                    if org == org_id and (org_id, source) not in current_statuses
                ]
                for source in sorted(resolved_sources):
                    sources_text.append(f"✅ {source}")

                sources_str = ", ".join(sources_text)
                data_office_link = self.generate_dataoffice_status_link_for_org(org_id)
                data["max_updated"] = data["max_updated"].replace(
                    microsecond=0
                )  # for better display
                statuses_message += f"\n| {org_id} | {sources_str} | {data["max_updated"]} | [DataOffice]({data_office_link}) |"

        if self.teams_webhook_url and statuses_message and statuses_changed:
            await send_to_channel(
                self.teams_webhook_url, statuses_message + "\n" + EXPLANATION
            )
            self.previous_statuses = current_statuses

    def generate_dataoffice_status_link_for_org(self, org_id):
        data_office_search_criteria = {
            "searchBuilder": {
                "criteria": [
                    {
                        "condition": "=",
                        "data": "Org ID",
                        "origData": "organization_id",
                        "type": "string",
                        "value": [org_id],
                    }
                ],
                "logic": "AND",
            }
        }

        quote = urllib.parse.quote(
            json.dumps(data_office_search_criteria, separators=(",", ":"))
        )

        data_office_link = (
            f"{self.data_office_url}/scraper_statuses/?tableState={quote}"
        )
        return data_office_link

    async def send_global_issue_alerts(
        self, scraper_statuses: ScraperStatusRepository
    ) -> None:
        sources = await scraper_statuses.get_alerting_sources(
            0.3, ignored_sources=["epic_sales"]
        )
        current_sources = {source[0] for source in sources}

        sources_changed = current_sources != self.previous_sources

        global_issues_message = ""
        if sources and sources_changed:
            global_issues_message += "### Global platform issues:\n\n"
            global_issues_message += "| source | affected |\n"
            global_issues_message += "|--------|----------|\n"
            for source in sources:
                source_name = source[0]
                if source_name not in self.previous_sources:
                    source_display = f"""🔺 {source_name}"""
                else:
                    source_display = source_name
                global_issues_message += (
                    f"| {source_display} | {int(source[1] * 100)} % affected |\n"
                )

            resolved_sources = self.previous_sources - current_sources
            for source in resolved_sources:
                global_issues_message += f"| {source} | ✅ Resolved |\n"

            global_issues_message += EXPLANATION

        if not sources and sources_changed:
            global_issues_message += "🎉🪄🎉 ALL ISSUES RESOLVED 🎉🪄🎉"

        if self.teams_webhook_url and global_issues_message and sources_changed:
            await send_to_channel(self.teams_webhook_url, global_issues_message)
            self.previous_sources = current_sources

    def reset_last_sent_message(self):
        self.previous_sources = set()
        self.previous_statuses = set()
