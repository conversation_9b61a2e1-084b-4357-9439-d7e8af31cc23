from functools import singledispatchmethod

from pydantic import ConfigDict

from scraper_service.logic.entities import OperationID, OrganizationID, Source, UserID
from scraper_service.logic.events.report_uploaded import ReportUploadedEvent
from scraper_service.logic.models.timestamped_base import TimestampedBaseModel


class Reports(TimestampedBaseModel):
    model_config = ConfigDict(from_attributes=True)

    organization_id: OrganizationID
    user_id: UserID
    source: Source

    operation_id: OperationID | None
    report_ids: list[int] = []

    @singledispatchmethod
    def apply(self, event) -> None:
        raise NotImplementedError(f"Event {event.__class__} not supported")

    @apply.register
    def _(self, event: ReportUploadedEvent) -> None:
        self.report_ids.append(event.body.report_info.id)
