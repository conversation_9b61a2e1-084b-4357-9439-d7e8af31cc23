from datetime import datetime
from functools import singledispatchmethod

from pydantic import ConfigDict

from scraper_service.logic.entities import (
    LoginState,
    OperationID,
    OrganizationID,
    OriginID,
    Source,
    UserID,
)
from scraper_service.logic.events.login_state_changed import LoginStateChangedEvent
from scraper_service.logic.models.timestamped_base import TimestampedBaseModel


class LoginStatus(TimestampedBaseModel):
    model_config = ConfigDict(from_attributes=True)

    organization_id: OrganizationID
    user_id: UserID
    source: Source

    state: LoginState = LoginState.UNCONFIGURED
    is_manual_session: bool | None = None
    consecutive_failed_login_count: int = 0
    last_success_timestamp: datetime | None = None
    last_fail_timestamp: datetime | None = None
    last_fail_reason: str | None = None
    last_origin: OriginID | None = None
    last_operation_id: OperationID | None = None
    version: int = 0

    @singledispatchmethod
    def apply(self, event) -> None:
        raise NotImplementedError(f"Event {event.__class__} not supported")

    @apply.register
    def _(self, event: LoginStateChangedEvent) -> None:
        self.updated_at = event.received_at
        self.user_id = event.user_id
        self.last_origin = event.origin
        self.last_operation_id = event.operation_id

        match event.body.new_state:
            case LoginState.SCHEDULED:
                self.state = LoginState.SCHEDULED
            case LoginState.STARTED:
                self.state = LoginState.STARTED
            case LoginState.CONFIGURED:
                self.state = LoginState.CONFIGURED
                self.last_success_timestamp = self.updated_at
                self.last_fail_reason = None
                self.consecutive_failed_login_count = 0
                self.is_manual_session = event.body.is_manual_session
            case LoginState.FAILED:
                self.state = LoginState.FAILED
                self.last_fail_timestamp = self.updated_at
                self.last_fail_reason = event.body.reason if event.body.reason else None
                self.consecutive_failed_login_count += 1
            case LoginState.STOPPED:
                self.state = LoginState.STOPPED
