from datetime import datetime
from enum import StrEnum
from functools import singledispatchmethod

from pydantic import ConfigDict

from scraper_service.logic.entities import (
    DateRange,
    LoginState,
    OperationID,
    OrganizationID,
    OriginID,
    ScraperBinaryStatus,
    Source,
    TriggeredBy,
    UserID,
)
from scraper_service.logic.events.login_state_changed import LoginStateChangedEvent
from scraper_service.logic.events.report_uploaded import ReportUploadedEvent
from scraper_service.logic.events.scraper_blockade_change import (
    ScraperBlockadeChangedEvent,
)
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedEvent
from scraper_service.logic.models.timestamped_base import TimestampedBaseModel


class ScraperCommand(StrEnum):
    SCRAPE = "SCRAPE"
    LOGIN = "LOGIN"


class ScraperStatus(TimestampedBaseModel):
    model_config = ConfigDict(from_attributes=True)

    organization_id: OrganizationID
    user_id: UserID
    source: Source

    state: ScraperBinaryStatus = ScraperBinaryStatus.UNCONFIGURED
    uses_manual_session: bool | None = None
    triggered_by: TriggeredBy | None = None
    consecutive_failed_scrape_count: int = 0
    last_success_timestamp: datetime | None = None
    last_fail_timestamp: datetime | None = None
    last_fail_reason: str | None = None
    last_command: ScraperCommand | None = None
    last_origin: OriginID | None = None
    last_operation_id: OperationID | None = None
    last_report_ids: list[int] | None = []
    last_scrape_date_ranges: list[DateRange] | None = []
    version: int = 0

    @singledispatchmethod
    def apply(self, event) -> None:
        raise NotImplementedError(f"Event {event.__class__} not supported")

    @apply.register
    def _(self, event: ScraperStateChangedEvent) -> None:
        if event.body.triggered_by == TriggeredBy.SHADOW_TASK:
            # Ignore events triggered by shadow tasks
            return

        self.updated_at = event.received_at
        self.last_origin = event.origin

        if self.last_operation_id != event.operation_id:
            self.last_report_ids = []
            self.last_scrape_date_ranges = []

        self.last_operation_id = event.operation_id
        self.triggered_by = event.body.triggered_by
        self.last_command = ScraperCommand.SCRAPE
        self.last_scrape_date_ranges = (
            event.body.date_ranges or self.last_scrape_date_ranges
        )

        if self.state == ScraperBinaryStatus.MANUALLY_BLOCKED:
            return

        match event.body.new_state:
            case ScraperBinaryStatus.SCHEDULED:
                self.state = ScraperBinaryStatus.SCHEDULED
            case ScraperBinaryStatus.STARTED:
                self.state = ScraperBinaryStatus.STARTED
            case ScraperBinaryStatus.FINISHED:
                self.state = ScraperBinaryStatus.FINISHED
                self.last_success_timestamp = self.updated_at
                self.last_fail_reason = None
                self.consecutive_failed_scrape_count = 0
            case ScraperBinaryStatus.FAILED:
                self.state = ScraperBinaryStatus.FAILED
                self.last_fail_timestamp = self.updated_at
                self.last_fail_reason = event.body.reason if event.body.reason else None
                self.consecutive_failed_scrape_count += 1
            case ScraperBinaryStatus.STOPPED:
                self.state = ScraperBinaryStatus.STOPPED
            case ScraperBinaryStatus.DISABLED:
                self.state = ScraperBinaryStatus.DISABLED

    @apply.register
    def _(self, event: LoginStateChangedEvent) -> None:
        if event.body.triggered_by == TriggeredBy.SHADOW_TASK:
            # Ignore events triggered by shadow tasks
            return

        self.updated_at = event.received_at
        self.last_origin = event.origin

        if self.last_operation_id != event.operation_id:
            self.last_report_ids = []

        self.last_operation_id = event.operation_id
        self.triggered_by = event.body.triggered_by
        self.last_command = ScraperCommand.LOGIN

        match event.body.new_state:
            case LoginState.SCHEDULED:
                self.state = ScraperBinaryStatus.SCHEDULED
            case LoginState.STARTED:
                self.state = ScraperBinaryStatus.STARTED
            case LoginState.CONFIGURED:
                self.state = ScraperBinaryStatus.CONFIGURED
                self.last_fail_reason = None
                self.consecutive_failed_scrape_count = 0
                self.uses_manual_session = event.body.is_manual_session
            case LoginState.FAILED:
                self.state = ScraperBinaryStatus.FAILED
                self.last_fail_timestamp = self.updated_at
                self.last_fail_reason = event.body.reason if event.body.reason else None
                self.consecutive_failed_scrape_count += 1
                self.uses_manual_session = None
            case LoginState.STOPPED:
                self.state = ScraperBinaryStatus.STOPPED

    @apply.register
    def _(self, event: ScraperBlockadeChangedEvent) -> None:
        self.updated_at = event.received_at
        self.last_origin = event.origin
        self.last_operation_id = None

        match event.body.new_state:
            case ScraperBinaryStatus.MANUALLY_BLOCKED:
                self.state = ScraperBinaryStatus.MANUALLY_BLOCKED
            case ScraperBinaryStatus.MANUALLY_UNBLOCKED:
                self.state = ScraperBinaryStatus.MANUALLY_UNBLOCKED

    @apply.register
    def _(self, event: ReportUploadedEvent) -> None:
        self.updated_at = event.received_at
        self.last_origin = event.origin
        self.last_operation_id = event.operation_id

        if self.last_report_ids is None:
            self.last_report_ids = []

        self.last_report_ids.append(event.body.report_info.id)


class ProjectionScraperStatus(ScraperStatus):
    user_email: str | None = None
    organization_name: str | None = None
