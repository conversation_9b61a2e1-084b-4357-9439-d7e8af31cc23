from datetime import datetime
from functools import singledispatchmethod

from pydantic import ConfigDict

from scraper_service.logic.entities import (
    LoginState,
    OperationID,
    OrganizationID,
    ScraperBinaryStatus,
    Source,
    TriggeredBy,
    UserID,
)
from scraper_service.logic.events.login_state_changed import LoginStateChangedEvent
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedEvent
from scraper_service.logic.models.scraper_status import Scraper<PERSON>ommand
from scraper_service.logic.models.timestamped_base import TimestampedBaseModel


class ScraperOperationHistory(TimestampedBaseModel):
    model_config = ConfigDict(from_attributes=True)

    organization_id: OrganizationID
    source: Source
    operation_id: OperationID

    state: ScraperBinaryStatus = ScraperBinaryStatus.UNCONFIGURED
    command: ScraperCommand = ScraperCommand.SCRAPE
    start_timestamp: datetime | None = None
    end_timestamp: datetime | None = None
    fail_reason: str | None = None
    user_id: UserID | None = None
    is_manual_session: bool | None = None
    triggered_by: TriggeredBy | None = None

    @singledispatchmethod
    def apply(self, event) -> None:
        raise NotImplementedError(f"Event {event.__class__} not supported")

    @apply.register
    def _(self, event: ScraperStateChangedEvent) -> None:
        self.updated_at = event.received_at
        self.user_id = event.user_id
        self.command = ScraperCommand.SCRAPE
        self.triggered_by = event.body.triggered_by

        match event.body.new_state:
            case ScraperBinaryStatus.STARTED:
                self.state = ScraperBinaryStatus.STARTED
                self.start_timestamp = self.updated_at
            case ScraperBinaryStatus.FINISHED:
                self.state = ScraperBinaryStatus.FINISHED
                self.end_timestamp = self.updated_at
            case ScraperBinaryStatus.FAILED:
                self.state = ScraperBinaryStatus.FAILED
                self.end_timestamp = self.updated_at
                self.fail_reason = event.body.reason
            case ScraperBinaryStatus.STOPPED:
                self.state = ScraperBinaryStatus.STOPPED
                self.end_timestamp = self.updated_at
            case ScraperBinaryStatus.DISABLED:
                self.state = ScraperBinaryStatus.DISABLED
                self.end_timestamp = self.updated_at

    @apply.register
    def _(self, event: LoginStateChangedEvent) -> None:
        self.updated_at = event.received_at
        self.user_id = event.user_id
        self.command = ScraperCommand.LOGIN
        self.triggered_by = event.body.triggered_by
        self.is_manual_session = event.body.is_manual_session

        match event.body.new_state:
            case LoginState.SCHEDULED:
                self.state = ScraperBinaryStatus.SCHEDULED
            case LoginState.STARTED:
                self.state = ScraperBinaryStatus.STARTED
                self.start_timestamp = self.updated_at
            case LoginState.CONFIGURED:
                self.state = ScraperBinaryStatus.CONFIGURED
                self.end_timestamp = self.updated_at
            case LoginState.FAILED:
                self.state = ScraperBinaryStatus.FAILED
                self.end_timestamp = self.updated_at
                self.fail_reason = event.body.reason
            case LoginState.STOPPED:
                self.state = ScraperBinaryStatus.STOPPED
                self.end_timestamp = self.updated_at
