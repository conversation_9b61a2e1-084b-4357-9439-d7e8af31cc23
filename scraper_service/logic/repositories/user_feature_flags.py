from datetime import datetime

from sqlalchemy import DateTime, Index, String, delete, select
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import Base
from scraper_service.logic.entities import UserFeatureFlags, UserID


class _UserFeatureFlagsModel(Base):
    __tablename__ = "users_feature_flags_cache"

    id: Mapped[int] = mapped_column(primary_key=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    user_id: Mapped[str] = mapped_column(String(100))
    feature_flags: Mapped[list[str]] = mapped_column(JSONB)

    __table_args__ = (
        Index(
            "ix_users_feature_flags_cache_user_id",
            user_id,
            unique=True,
        ),
    )


class UserFeatureFlagsRepository:
    def __init__(self, session: AsyncSession) -> None:
        self._session: AsyncSession = session

    async def _get_user_model(self, user_id) -> _UserFeatureFlagsModel | None:
        stmt = select(_UserFeatureFlagsModel).where(
            _UserFeatureFlagsModel.user_id == user_id
        )
        result = await self._session.execute(stmt)
        return result.scalar_one_or_none()

    async def save(self, entity: UserFeatureFlags) -> None:
        existing_entity = await self._get_user_model(entity.id)
        if existing_entity:
            existing_entity.feature_flags = entity.feature_flags
        else:
            new_entity = _UserFeatureFlagsModel(
                user_id=entity.id, feature_flags=entity.feature_flags
            )
            self._session.add(new_entity)

    async def get(
        self,
        user_id: UserID,
    ) -> UserFeatureFlags:
        entity = await self._get_user_model(user_id)

        if entity:
            return UserFeatureFlags(
                id=entity.user_id,
                feature_flags=entity.feature_flags,
            )
        else:
            return UserFeatureFlags(id=user_id, feature_flags=[])

    async def bulk_override(self, entities: list[UserFeatureFlags]) -> None:
        # Delete all existing rows from the table
        delete_stmt = delete(_UserFeatureFlagsModel)
        await self._session.execute(delete_stmt)

        # If entities list is empty, we're done (all rows deleted)
        if not entities:
            return

        # Bulk insert all new entities
        new_entities = [
            _UserFeatureFlagsModel(
                user_id=entity.id, feature_flags=entity.feature_flags
            )
            for entity in entities
        ]
        self._session.add_all(new_entities)
