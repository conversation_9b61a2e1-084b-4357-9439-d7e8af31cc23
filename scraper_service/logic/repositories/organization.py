from datetime import datetime, timedelta

from sqlalchemy import DateTime, Index, String, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import Base
from scraper_service.logic.entities import Organization, OrganizationID

DEFAULT_TIME_TO_LIFE = timedelta(days=1)


class _OrganizationModel(Base):
    __tablename__ = "organizations_cache"

    id: Mapped[int] = mapped_column(primary_key=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    organization_id: Mapped[str] = mapped_column(String(100))
    name: Mapped[str] = mapped_column(String(100))
    __table_args__ = (
        Index(
            "ix_organizations_cache_organization_id",
            organization_id,
            unique=True,
        ),
    )


class OrganizationRepository:
    def __init__(
        self,
        session: AsyncSession,
    ) -> None:
        self._session: AsyncSession = session

    async def _get_organization_model(
        self, organization_id
    ) -> _OrganizationModel | None:
        stmt = select(_OrganizationModel).where(
            _OrganizationModel.organization_id == organization_id
        )
        result = await self._session.execute(stmt)
        return result.scalar_one_or_none()

    async def save(
        self,
        entity: Organization,
    ) -> None:
        existing_entity = await self._get_organization_model(entity.id)
        if existing_entity:
            existing_entity.name = entity.name
        else:
            new_entity = _OrganizationModel(organization_id=entity.id, name=entity.name)
            self._session.add(new_entity)

    async def get(self, org_id: OrganizationID):
        entity = await self._get_organization_model(org_id)

        if entity:
            return Organization(
                id=entity.organization_id,
                name=entity.name,
                updated_at=entity.updated_at,
            )
        else:
            return Organization(id=org_id, name="")
