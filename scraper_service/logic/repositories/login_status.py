from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Index, Integer, Select, String, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import DatabaseBaseModel
from scraper_service.logic.entities import OrganizationID, Source, UserID
from scraper_service.logic.models.login_status import LoginStatus


class _LoginStatusModel(DatabaseBaseModel):
    __tablename__ = "login_status_projection"

    organization_id: Mapped[str] = mapped_column(String(100))
    source: Mapped[str] = mapped_column(String(100))
    state: Mapped[str] = mapped_column(String(100))
    is_manual_session: Mapped[bool | None] = mapped_column(Boolean)
    consecutive_failed_login_count: Mapped[int] = mapped_column(Integer, default=0)
    last_success_timestamp: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True)
    )
    last_fail_timestamp: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True)
    )
    last_fail_reason: Mapped[str | None] = mapped_column(String(100))
    user_id: Mapped[str] = mapped_column(String(100))
    last_origin: Mapped[str | None]
    version: Mapped[int] = mapped_column(Integer, default=0)
    last_operation_id: Mapped[str | None] = mapped_column(String(100))
    __table_args__ = (
        Index(
            "ix_login_status_projection_organization_id_user_id_source",
            organization_id,
            user_id,
            source,
            unique=True,
        ),
    )


class LoginStatusRepository:
    def __init__(self, session: AsyncSession) -> None:
        self._session: AsyncSession = session

    async def save(self, entity: LoginStatus) -> None:
        stmt = select(_LoginStatusModel).where(
            _LoginStatusModel.organization_id == entity.organization_id,
            _LoginStatusModel.user_id == entity.user_id,
            _LoginStatusModel.source == entity.source,
        )
        result = await self._session.execute(stmt)
        existing_entity = result.one_or_none()

        if existing_entity is not None:
            for field, value in entity.model_dump().items():
                setattr(existing_entity[0], field, value)
        else:
            new_entity = _LoginStatusModel(**entity.model_dump())
            self._session.add(new_entity)

    async def get(
        self, organization_id: OrganizationID, user_id: UserID, source: Source
    ) -> LoginStatus:
        stmt = select(
            _LoginStatusModel,
        ).where(
            _LoginStatusModel.organization_id == organization_id,
            _LoginStatusModel.user_id == user_id,
            _LoginStatusModel.source == source,
        )

        result = await self._session.execute(stmt)
        entity = result.scalar_one_or_none()

        if entity:
            return LoginStatus.model_validate(entity)

        return LoginStatus(
            organization_id=organization_id, user_id=user_id, source=source
        )

    async def get_all(
        self,
        organization_id: OrganizationID | None = None,
        organization_ids: list[OrganizationID] | None = None,
        source: Source | None = None,
        ignored_sources: list[Source] | None = None,
    ) -> list[LoginStatus]:
        stmt: Select = select(_LoginStatusModel)

        if organization_id is not None:
            stmt = stmt.where(_LoginStatusModel.organization_id == organization_id)
        if organization_ids:
            stmt = stmt.where(_LoginStatusModel.organization_id.in_(organization_ids))
        if source is not None:
            stmt = stmt.where(_LoginStatusModel.source == source)
        if ignored_sources:
            stmt = stmt.where(_LoginStatusModel.source.notin_(ignored_sources))

        result = await self._session.execute(stmt)
        entities = result.scalars().all()

        return [LoginStatus.model_validate(entity) for entity in entities]
