from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import DatabaseBaseModel
from scraper_service.logic.events.report_uploaded import ReportUploadedEvent
from scraper_service.logic.models.reports import Reports


class _ReportsModel(DatabaseBaseModel):
    __tablename__ = "reports_projection"

    organization_id: Mapped[str] = mapped_column(String(100))
    user_id: Mapped[str] = mapped_column(String(100))
    source: Mapped[str] = mapped_column(String(100))
    operation_id: Mapped[str | None] = mapped_column(String(100))
    report_ids: Mapped[list[int] | None] = mapped_column(ARRAY(Integer))

    version: Mapped[int] = mapped_column(Integer, default=0)


class ReportsRepository:
    def __init__(self, session: AsyncSession) -> None:
        self._session: AsyncSession = session

    async def save(self, entity: Reports) -> None:
        stmt = select(_ReportsModel).where(
            _ReportsModel.operation_id == entity.operation_id
        )
        result = await self._session.execute(stmt)
        existing_entity = result.one_or_none()

        if existing_entity is not None:
            for field, value in entity.model_dump().items():
                setattr(existing_entity[0], field, value)
        else:
            new_entity = _ReportsModel(**entity.model_dump())
            self._session.add(new_entity)

    async def get(self, event: ReportUploadedEvent) -> Reports:
        stmt = select(_ReportsModel).where(
            _ReportsModel.operation_id == event.operation_id
        )

        result = await self._session.execute(stmt)
        entity = result.scalar_one_or_none()

        if entity:
            return Reports.model_validate(entity)

        return Reports(
            operation_id=event.operation_id,
            organization_id=event.organization_id,
            user_id=event.user_id,
            source=event.body.report_info.source,
        )
