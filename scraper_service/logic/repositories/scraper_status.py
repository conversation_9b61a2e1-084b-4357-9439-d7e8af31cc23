from datetime import datetime, <PERSON><PERSON>ta
from typing import Any

from sqlalchemy import (
    ARRAY,
    Boolean,
    DateTime,
    Index,
    Integer,
    Select,
    String,
    and_,
    case,
    func,
    select,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import DatabaseBaseModel
from scraper_service.datatables import DataTable
from scraper_service.logic.entities import (
    OrganizationID,
    ScraperBinaryStatus,
    Source,
    UserID,
)
from scraper_service.logic.models.scraper_status import ScraperCommand, ScraperStatus


class _ScraperStatusModel(DatabaseBaseModel):
    __tablename__ = "scraper_status"

    organization_id: Mapped[str] = mapped_column(String(100))
    user_id: Mapped[str] = mapped_column(String(100))
    source: Mapped[str] = mapped_column(String(100))
    state: Mapped[str] = mapped_column(String(100))
    uses_manual_session: Mapped[bool | None] = mapped_column(Boolean)
    triggered_by: Mapped[str | None] = mapped_column(String(100))
    consecutive_failed_scrape_count: Mapped[int] = mapped_column(Integer, default=0)
    last_success_timestamp: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True)
    )
    last_fail_timestamp: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True)
    )
    last_fail_reason: Mapped[str | None] = mapped_column(String(100))
    last_command: Mapped[str | None] = mapped_column(String(100))
    last_origin: Mapped[str | None]
    version: Mapped[int] = mapped_column(Integer, default=0)
    last_operation_id: Mapped[str | None] = mapped_column(String(100))
    last_report_ids: Mapped[list[int] | None] = mapped_column(ARRAY(Integer))
    last_scrape_date_ranges: Mapped[list[dict] | None] = mapped_column(JSONB)
    __table_args__ = (
        Index(
            "ix_scraper_status_projection_organization_id_user_id_source",
            organization_id,
            user_id,
            source,
            unique=True,
        ),
    )


class ScraperStatusRepository:
    def __init__(
        self,
        session: AsyncSession,
    ) -> None:
        self._session: AsyncSession = session

    async def save(self, entity: ScraperStatus) -> None:
        stmt = select(_ScraperStatusModel).where(
            _ScraperStatusModel.organization_id == entity.organization_id,
            _ScraperStatusModel.user_id == entity.user_id,
            _ScraperStatusModel.source == entity.source,
        )
        result = await self._session.execute(stmt)
        existing_entity = result.one_or_none()

        if existing_entity is not None:
            for field, value in entity.model_dump().items():
                setattr(existing_entity[0], field, value)
        else:
            new_entity = _ScraperStatusModel(**entity.model_dump())
            self._session.add(new_entity)

    async def get(
        self, organization_id: OrganizationID, user_id: UserID, source: Source
    ) -> ScraperStatus:
        stmt = select(
            _ScraperStatusModel,
        ).where(
            _ScraperStatusModel.organization_id == organization_id,
            _ScraperStatusModel.user_id == user_id,
            _ScraperStatusModel.source == source,
        )

        result = await self._session.execute(stmt)
        entity = result.scalar_one_or_none()

        if entity:
            return ScraperStatus.model_validate(entity)

        return ScraperStatus(
            organization_id=organization_id, user_id=user_id, source=source
        )

    async def get_datatable_format(self, params: dict[str, Any]):
        datatable: DataTable = DataTable(
            session=self._session,
            table=_ScraperStatusModel,
        )
        results = await datatable.execute(request_params=params)
        return results

    async def get_all(
        self,
        organization_id: OrganizationID | None = None,
        organization_ids: list[OrganizationID] | None = None,
        source: Source | None = None,
        ignored_sources: list[Source] | None = None,
    ) -> list[ScraperStatus]:
        stmt: Select = select(_ScraperStatusModel)

        if organization_id is not None:
            stmt = stmt.where(_ScraperStatusModel.organization_id == organization_id)
        if organization_ids:
            stmt = stmt.where(_ScraperStatusModel.organization_id.in_(organization_ids))
        if source is not None:
            stmt = stmt.where(_ScraperStatusModel.source == source)
        if ignored_sources:
            stmt = stmt.where(_ScraperStatusModel.source.notin_(ignored_sources))

        result = await self._session.execute(stmt)
        entities = result.scalars().all()

        return [ScraperStatus.model_validate(entity) for entity in entities]

    async def get_statuses_needing_attention(
        self,
        organization_ids: list[OrganizationID] | None = None,
        ignored_sources: list[Source] | None = None,
    ) -> list[ScraperStatus]:
        return (
            []
            + await self.get_failed_scrape_statuses(organization_ids, ignored_sources)
            + await self.get_long_running_scrapes(organization_ids, ignored_sources)
        )

    async def get_failed_scrape_statuses(
        self,
        organization_ids: list[OrganizationID] | None = None,
        ignored_sources: list[Source] | None = None,
    ) -> list[ScraperStatus]:
        stmt: Select = select(_ScraperStatusModel).where(
            _ScraperStatusModel.state == "FAILED",
            _ScraperStatusModel.last_command != ScraperCommand.LOGIN,
        )

        if organization_ids:
            stmt = stmt.where(_ScraperStatusModel.organization_id.in_(organization_ids))

        if ignored_sources:
            stmt = stmt.where(_ScraperStatusModel.source.notin_(ignored_sources))

        result = await self._session.execute(stmt)
        entities = result.scalars().all()

        return [ScraperStatus.model_validate(entity) for entity in entities]

    async def get_long_running_scrapes(
        self,
        organization_ids: list[OrganizationID] | None = None,
        ignored_sources: list[Source] | None = None,
    ) -> list[ScraperStatus]:
        cutoff_time = datetime.utcnow() - timedelta(days=1)
        stmt: Select = select(_ScraperStatusModel).where(
            and_(
                _ScraperStatusModel.state == ScraperBinaryStatus.STARTED,
                _ScraperStatusModel.updated_at < cutoff_time,
            )
        )

        if organization_ids:
            stmt = stmt.where(_ScraperStatusModel.organization_id.in_(organization_ids))

        if ignored_sources:
            stmt = stmt.where(_ScraperStatusModel.source.notin_(ignored_sources))

        result = await self._session.execute(stmt)
        entities = result.scalars().all()

        return [ScraperStatus.model_validate(entity) for entity in entities]

    async def get_alerting_sources(
        self,
        fail_percentage,
        ignored_sources: list[Source] | None = None,
    ) -> list[tuple[Source, float]]:
        failed_case = func.sum(
            case(
                (
                    and_(
                        _ScraperStatusModel.state == ScraperBinaryStatus.FAILED,
                        _ScraperStatusModel.last_command == ScraperCommand.SCRAPE,
                    ),
                    1,
                ),
                else_=0,
            )
        )

        not_failed_case = func.sum(
            case(
                (
                    _ScraperStatusModel.state.in_([
                        ScraperBinaryStatus.STARTED,
                        ScraperBinaryStatus.SCHEDULED,
                        ScraperBinaryStatus.FINISHED,
                    ]),
                    1,
                ),
                else_=0,
            )
        )
        all_cases = failed_case + not_failed_case

        failure_ratio = case(
            (all_cases == 0, 0),
            else_=(failed_case / all_cases),
        ).label("failure_ratio")

        stmt = (
            select(
                _ScraperStatusModel.source,
                failure_ratio,
            )
            .group_by(_ScraperStatusModel.source)
            .having(func.count(_ScraperStatusModel.id) > 0)
            .having(failure_ratio > fail_percentage)
        )
        if ignored_sources:
            stmt = stmt.where(_ScraperStatusModel.source.notin_(ignored_sources))

        results = (await self._session.execute(stmt)).all()

        return [
            (result.source, result.failure_ratio)
            for result in results
            if result.failure_ratio is not None
        ]
