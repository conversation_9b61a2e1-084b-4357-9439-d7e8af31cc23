from datetime import datetime

from sqlalchemy import DateTime, Index, String, delete, select
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import Base


class _OrganizationFeatureFlagsModel(Base):
    __tablename__ = "organizations_feature_flags_cache"

    id: Mapped[int] = mapped_column(primary_key=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    organization_id: Mapped[str] = mapped_column(String(100))
    feature_flags: Mapped[list[str]] = mapped_column(JSONB)

    __table_args__ = (
        Index(
            "ix_organizations_feature_flags_cache_organization_id",
            organization_id,
            unique=True,
        ),
    )
