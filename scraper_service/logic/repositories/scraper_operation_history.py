from datetime import datetime
from typing import Any

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, String, UniqueConstraint, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import DatabaseBaseModel
from scraper_service.datatables import DataTable
from scraper_service.logic.entities import OrganizationID, Source
from scraper_service.logic.events.login_state_changed import LoginStateChangedEvent
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedEvent
from scraper_service.logic.models.scraper_operation_history import (
    ScraperOperationHistory,
)
from scraper_service.logic.models.scraper_status import ScraperCommand


class _ScraperOperationHistoryModel(DatabaseBaseModel):
    __tablename__ = "scraper_operation_history"

    organization_id: Mapped[str] = mapped_column(String(100), index=True)
    source: Mapped[str] = mapped_column(String(100))
    operation_id: Mapped[str] = mapped_column(String(100))
    user_id: Mapped[str] = mapped_column(String(100))
    state: Mapped[str] = mapped_column(String(100))
    command: Mapped[str] = mapped_column(
        String(100), server_default=ScraperCommand.SCRAPE
    )

    start_timestamp: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    end_timestamp: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    fail_reason: Mapped[str | None] = mapped_column(String(100))
    triggered_by: Mapped[str | None] = mapped_column(String(100))
    is_manual_session: Mapped[bool | None] = mapped_column(Boolean)
    __table_args__ = (
        UniqueConstraint(
            "organization_id",
            "source",
            "operation_id",
            name="uq_organization_source_operation",
        ),
    )


class ScraperOperationHistoryRepository:
    def __init__(self, session: AsyncSession) -> None:
        self._session: AsyncSession = session

    async def save(self, entity: ScraperOperationHistory) -> None:
        stmt = select(_ScraperOperationHistoryModel).where(
            _ScraperOperationHistoryModel.operation_id == entity.operation_id
        )
        result = await self._session.execute(stmt)
        existing_entity = result.one_or_none()

        if existing_entity is not None:
            for field, value in entity.model_dump().items():
                setattr(existing_entity[0], field, value)
        else:
            new_entity = _ScraperOperationHistoryModel(**entity.model_dump())
            self._session.add(new_entity)

    async def get_for(
        self, event: ScraperStateChangedEvent | LoginStateChangedEvent
    ) -> ScraperOperationHistory:
        stmt = select(
            _ScraperOperationHistoryModel,
        ).where(_ScraperOperationHistoryModel.operation_id == event.operation_id)

        result = await self._session.execute(stmt)
        entity = result.one_or_none()

        if entity:
            return ScraperOperationHistory.model_validate(entity[0])

        return ScraperOperationHistory(
            organization_id=event.organization_id,
            source=event.body.source,
            operation_id=event.operation_id or "",
        )

    async def get_all(
        self,
        organization_id: OrganizationID | None = None,
        source: Source | None = None,
    ) -> list[ScraperOperationHistory]:
        stmt = select(_ScraperOperationHistoryModel)

        if organization_id is not None:
            stmt = stmt.where(
                _ScraperOperationHistoryModel.organization_id == organization_id
            )
        if source is not None:
            stmt = stmt.where(_ScraperOperationHistoryModel.source == source)

        result = await self._session.execute(stmt)
        entities = result.scalars().all()

        return [ScraperOperationHistory.model_validate(entity) for entity in entities]

    async def get_datatable_format(self, params: dict[str, Any]):
        datatable: DataTable = DataTable(
            session=self._session,
            table=_ScraperOperationHistoryModel,
        )
        results = await datatable.execute(request_params=params)
        return results
