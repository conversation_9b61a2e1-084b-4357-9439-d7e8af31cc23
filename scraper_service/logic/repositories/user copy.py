from datetime import datetime

from sqlalchemy import DateTime, Index, String, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import Base
from scraper_service.logic.entities import User, UserID


class _UserModel(Base):
    __tablename__ = "users_cache"

    id: Mapped[int] = mapped_column(primary_key=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=datetime.now()
    )
    user_id: Mapped[str] = mapped_column(String(100))
    email: Mapped[str] = mapped_column(String(100))
    __table_args__ = (
        Index(
            "ix_users_cache_user_id",
            user_id,
            unique=True,
        ),
    )


class UserRepository:
    def __init__(
        self,
        session: AsyncSession,
    ) -> None:
        self._session: AsyncSession = session

    async def _get_user_model(self, user_id) -> _UserModel | None:
        stmt = select(_UserModel).where(_UserModel.user_id == user_id)
        result = await self._session.execute(stmt)
        return result.scalar_one_or_none()

    async def save(self, entity: User) -> None:
        existing_entity = await self._get_user_model(entity.id)
        if existing_entity:
            existing_entity.email = entity.email
        else:
            new_entity = _UserModel(user_id=entity.id, email=entity.email)
            self._session.add(new_entity)

    async def get(
        self,
        user_id: UserID,
    ) -> User:
        entity = await self._get_user_model(user_id)

        if entity:
            return User(
                id=entity.user_id,
                email=entity.email,
                updated_at=entity.updated_at,
            )
        else:
            return User(id=user_id, email="")
