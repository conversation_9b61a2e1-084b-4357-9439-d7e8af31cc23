class DomainBaseException(Exception):
    def __init__(self, detail: str) -> None:
        self.details = detail
        super().__init__(self.details)


class CommandNotSupported(DomainBaseException):
    def __init__(self) -> None:
        super().__init__("Command not supported")


class ScraperNotConfigured(DomainBaseException):
    def __init__(self) -> None:
        super().__init__("<PERSON>raper not configured")
