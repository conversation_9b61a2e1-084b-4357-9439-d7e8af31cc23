from fastapi import APIRouter, Depends

from scraper_service.api.dependencies import (
    AnyKnownEvent,
    ScraperApplicationDependency,
    get_current_user,
)
from scraper_service.logic.scraper import HandleEventCommand

external_router = APIRouter(prefix="/scraper_events")


@external_router.post("")
async def register_new_scraper_event(
    event: AnyKnownEvent,
    application: ScraperApplicationDependency,
    current_user=Depends(get_current_user),
):
    command = HandleEventCommand(event=event, current_user=current_user)
    return await application.handle(command)
