import base64
import json
import logging
from datetime import datetime
from enum import Enum
from typing import Any

from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

from scraper_service.api.dependencies import get_current_user
from scraper_service.logic.entities import OrganizationID, OriginID, Source, UserID

Base64EncodedString = str

external_router = APIRouter(prefix="/traces")


class LogLevel(int, Enum):
    INFO = 1
    WARNING = 2
    ERROR = 3


# TODO: I'm doing that during patodevstream, it looks like it works but if you consider it ugly
# find someone to change it.
def map_log_level(level: int) -> str:
    if level == LogLevel.INFO:
        return "INFO"
    elif level == LogLevel.WARNING:
        return "WARNING"
    elif level == LogLevel.ERROR:
        return "ERROR"
    else:
        return "UNKNOWN"


standardized_custom_fields = ["manager_version", "entryType", "command"]


def decode_custom_fields(
    encoded_custom_fields: Base64EncodedString | None,
) -> tuple[dict[str, Any], str | None]:
    """
    Extracts standardized custom fields from a Base64 encoded string.
    Returns a tuple of the extracted fields as a dictionary and the rest of the fields as a string
    """

    if not encoded_custom_fields:
        return {}, None

    decoded_fields = json.loads(base64.b64decode(encoded_custom_fields).decode("utf-8"))
    standardized_fields = {
        key: decoded_fields[key]
        for key in standardized_custom_fields
        if key in decoded_fields
    }
    additional_custom_fields = {
        key: value
        for key, value in decoded_fields.items()
        if key not in standardized_custom_fields
    }
    return standardized_fields, json.dumps(
        additional_custom_fields
    ) if additional_custom_fields else None


class Trace(BaseModel):
    message: Base64EncodedString
    level: LogLevel = LogLevel.INFO
    client_timestamp: datetime
    origin: OriginID
    user_id: UserID = Field("not-provided", validate_default=False)
    organization_id: OrganizationID = Field("not-provided", validate_default=False)
    source: Source | None = None
    custom_fields: Base64EncodedString | None = None
    operation_id: str | None = None
    machine_info: dict[str, str] | None = (
        None  # This should be mandatory, but we need an intro period for old scraperLibs
    )


binaries_output_logger = logging.getLogger("binaries_output_logger")


# TODO: verify traces for a accessible org
@external_router.put("")
async def capture_traces(traces: list[Trace], current_user=Depends(get_current_user)):
    """
    This endpoint should forward the telemetry data to elastic.
    """
    for trace in traces:
        try:
            decoded_message = (
                base64.b64decode(trace.message).decode("utf-8") if trace.message else ""
            )
            custom_fields, nonstandard_custom_fields = decode_custom_fields(
                trace.custom_fields
            )
        except Exception:
            raise ValueError("Failed to decode base64 message")

        # TODO: in case of api key, refactor later
        user_id = current_user["user_id"] if current_user else trace.user_id
        organization_id = (
            current_user["main_organization_id"]
            if current_user
            else trace.organization_id
        )
        binaries_output_logger.log(
            level=logging.getLevelNamesMapping()[map_log_level(trace.level)],
            msg=decoded_message,
            extra={
                "user_id": user_id,
                "organization_id": organization_id,
                "s2.client_timestamp": trace.client_timestamp.isoformat(),
                "s2.operation_id": trace.operation_id,
                "s2.origin": trace.origin,
                "s2.source": trace.source,
                "s2.machine_info": trace.machine_info,
                **{f"s2.{key}": value for key, value in custom_fields.items()},
                **(
                    {"s2.nonstandard_custom_fields": nonstandard_custom_fields}
                    if nonstandard_custom_fields
                    else {}
                ),
            },
        )
