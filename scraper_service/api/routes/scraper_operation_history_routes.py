from fastapi import APIRouter, Request

from scraper_service.api.dependencies import ScraperOperationHistoryRepositoryDependency
from scraper_service.logic.entities import OrganizationID, Source

internal_router = APIRouter(prefix="/scraper_operation_history")


@internal_router.get("")
async def get_scraper_operation_history(
    history_repo: ScraperOperationHistoryRepositoryDependency,
    organization_id: OrganizationID | None = None,
    source: Source | None = None,
):
    return await history_repo.get_all(organization_id=organization_id, source=source)


@internal_router.get("/datatable")
async def get_scraper_operation_history_datatable_format(
    request: Request,
    history_repo: ScraperOperationHistoryRepositoryDependency,
):
    query_params = dict(request.query_params)
    response = await history_repo.get_datatable_format(query_params)

    return response
