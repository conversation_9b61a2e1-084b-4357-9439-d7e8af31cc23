from fastapi import APIRouter

from scraper_service.api.dependencies import LoginStatusRepositoryDependency
from scraper_service.logic.entities import OrganizationID
from scraper_service.logic.models.login_status import LoginStatus

internal_router = APIRouter(prefix="/login_state")


@internal_router.get("/all")
async def get_all_statuses(
    status_repository: LoginStatusRepositoryDependency,
    organization_id: OrganizationID | None = None,
) -> list[LoginStatus]:
    return await status_repository.get_all(organization_id=organization_id)
