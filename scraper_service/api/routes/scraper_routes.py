from fastapi import APIRouter, HTTPException

from scraper_service.api.dependencies import ScraperApplicationDependency
from scraper_service.logic.exception import ScraperNotConfigured
from scraper_service.logic.scraper import BlockCommand, UnblockCommand

internal_router = APIRouter(prefix="/scraper")


@internal_router.post("/block")
async def block_scraper(
    command: BlockCommand,
    application: ScraperApplicationDependency,
):
    try:
        return await application.handle(command)
    except ScraperNotConfigured as e:
        raise HTTPException(status_code=404, detail=str(e))


@internal_router.post("/unblock")
async def unblock_scraper(
    command: UnblockCommand,
    application: ScraperApplicationDependency,
):
    try:
        return await application.handle(command)
    except <PERSON>raperNotConfigured as e:
        raise HTTPException(status_code=404, detail=str(e))
