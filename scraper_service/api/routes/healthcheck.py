import logging
import math
import os
import time
from datetime import datetime

import psutil
from dateutil.relativedelta import relativedelta
from fastapi import APIRouter
from sqlalchemy import exc, func, select, table
from starlette.requests import Request

from scraper_service.api.dependencies import AsyncSessionDependency, ConfigDependency

HEALTHCHECK_URL = "/health"
public_router = APIRouter(prefix=HEALTHCHECK_URL)


# filter healthcheck access logs
class EndpointLogFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        return record.getMessage().find(HEALTHCHECK_URL) == -1


logging.getLogger("uvicorn.access").addFilter(EndpointLogFilter())


@public_router.get("")
async def health(
    request: Request,
    config: ConfigDependency,
):
    start_time = datetime.fromtimestamp(psutil.Process(os.getpid()).create_time())
    now = datetime.now()
    uptime = relativedelta(now, start_time)
    uptime_in_seconds = math.floor((now - start_time).total_seconds())
    return {
        "serviceName": config.service_name,
        "instance": os.uname(),
        "version": config.app_version,
        "docker": {
            "tag": config.docker_tag,
            "buildTimestamp": config.docker_build_timestamp,
        },
        "uptime": {
            "seconds": uptime_in_seconds,
            "readable": f"{uptime.years} years, {uptime.months} months, {uptime.days} days, {uptime.hours} hours, {uptime.minutes} minutes, {uptime.seconds} seconds",
        },
    }


@public_router.get("/synthetic")
async def synthetic_health(
    session: AsyncSessionDependency,
):
    response: dict[str, bool | float | None] = {
        "database_connected": False,
        "database_duration": None,
    }
    try:
        start = time.perf_counter()
        stmt = select(func.count()).select_from(table("pg_tables", schema="pg_catalog"))
        tables_count = await session.scalar(stmt)
        response["database_duration"] = time.perf_counter() - start
        response["database_connected"] = bool(tables_count)
    except exc.SQLAlchemyError:
        pass
    return response
