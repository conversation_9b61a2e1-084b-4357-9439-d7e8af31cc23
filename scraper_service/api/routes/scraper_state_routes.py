from fastapi import APIRouter, Depends, Request

from scraper_service.api.auth import Unauthorized, user_has_access_to_organization
from scraper_service.api.dependencies import (
    ScraperStatusProjectionDependency,
    get_current_user,
)
from scraper_service.logic.entities import OrganizationID
from scraper_service.logic.models.scraper_status import ProjectionScraperStatus

external_router = APIRouter(prefix="/scraper_state")
internal_router = APIRouter(prefix="/scraper_state")


@external_router.get("/current/my")
async def get_current_status(
    status_projection: ScraperStatusProjectionDependency,
    current_user=Depends(get_current_user),
) -> list[ProjectionScraperStatus]:
    organization_id = current_user["main_organization_id"]
    if not user_has_access_to_organization(current_user, organization_id):
        raise Unauthorized(detail="User does not have access to this organization")
    return await status_projection.get_all(organization_id=organization_id)


@internal_router.get("/all")
async def get_all_statuses(
    status_projection: ScraperStatusProjectionDependency,
    organization_id: OrganizationID | None = None,
) -> list[ProjectionScraperStatus]:
    return await status_projection.get_all(organization_id=organization_id)


@internal_router.get("/datatable")
async def get_statuses_datatable(
    request: Request,
    status_projection: ScraperStatusProjectionDependency,
):
    query_params = dict(request.query_params)
    response = await status_projection.get_datatable_format(query_params)

    return response
