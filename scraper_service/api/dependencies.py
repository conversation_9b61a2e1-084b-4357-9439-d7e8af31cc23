import logging
from collections.abc import AsyncGenerator
from typing import Annotated

from fastapi import Depends, HTTPException, Security
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from pydantic import Field
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.asyncio.engine import AsyncEngine

from scraper_service.api.auth import TokenValidationError, get_user_from_jwt
from scraper_service.config import Config
from scraper_service.connectors.db import setup_token_refresh
from scraper_service.connectors.user_service_client import UserServiceClient
from scraper_service.logic.events.event import EventsRepository
from scraper_service.logic.events.login_state_changed import LoginStateChangedEvent
from scraper_service.logic.events.report_uploaded import ReportUploadedEvent
from scraper_service.logic.events.scraper_blockade_change import (
    ScraperBlockadeChangedEvent,
)
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedEvent
from scraper_service.logic.projections.generic_projection import GenericProjection
from scraper_service.logic.projections.organization_aggragator import (
    OrganizationAggregator,
)
from scraper_service.logic.projections.scraper_state_projection import (
    ScraperStatusProjection,
)
from scraper_service.logic.projections.user_aggragator import UserAggregator
from scraper_service.logic.repositories.login_status import LoginStatusRepository
from scraper_service.logic.repositories.organization import OrganizationRepository
from scraper_service.logic.repositories.reports import ReportsRepository
from scraper_service.logic.repositories.scraper_operation_history import (
    ScraperOperationHistoryRepository,
)
from scraper_service.logic.repositories.scraper_status import ScraperStatusRepository
from scraper_service.logic.repositories.user import UserRepository
from scraper_service.logic.scraper import ScraperApplication

logger: logging.Logger = logging.getLogger(__name__)


def get_config() -> Config:
    return Config()  # type: ignore  # noqa: PGH003


ConfigDependency = Annotated[Config, Depends(get_config)]


def get_current_user(
    config: ConfigDependency,
    auth_token: HTTPAuthorizationCredentials = Security(HTTPBearer()),
):
    try:
        return get_user_from_jwt(
            auth_token.credentials, config.user_access_token_public_key
        )
    except TokenValidationError as e:
        raise HTTPException(status_code=403, detail=str(e))


AnyKnownEvent = Annotated[
    ScraperStateChangedEvent
    | LoginStateChangedEvent
    | ScraperBlockadeChangedEvent
    | ReportUploadedEvent,
    Field(discriminator="event_type"),
]


_async_db_engine: AsyncEngine | None = None


async def get_async_db_engine(config: Config = Depends(get_config)) -> AsyncEngine:
    global _async_db_engine
    if _async_db_engine is None:
        _async_db_engine = create_async_engine(
            config.database_url, pool_size=config.database_pool_size
        )
        if "azure_identity_token" in config.database_url:
            logger.info("Setting up Azure token authentication for DB connection")
            setup_token_refresh(_async_db_engine.sync_engine)

    return _async_db_engine


_async_session_maker: async_sessionmaker[AsyncSession] | None = None


async def get_async_session(
    async_db_engine=Depends(get_async_db_engine),
) -> AsyncGenerator[AsyncSession]:
    global _async_session_maker
    if _async_session_maker is None:
        _async_session_maker = async_sessionmaker(
            async_db_engine, expire_on_commit=False
        )
    async with _async_session_maker.begin() as session:
        yield session


AsyncSessionDependency = Annotated[AsyncSession, Depends(get_async_session)]


def get_user_service_client(config: ConfigDependency) -> UserServiceClient:
    return UserServiceClient(config)


UserServiceClientDependency = Annotated[
    UserServiceClient, Depends(get_user_service_client)
]


def get_user_repo(session: AsyncSessionDependency) -> UserRepository:
    return UserRepository(session)


UserRepositoryDependency = Annotated[UserRepository, Depends(get_user_repo)]


def get_organization_repo(session: AsyncSessionDependency) -> OrganizationRepository:
    return OrganizationRepository(
        session,
    )


OrganizationRepositoryDependency = Annotated[
    OrganizationRepository, Depends(get_organization_repo)
]


def get_user_aggregator(
    user_repo: UserRepositoryDependency,
    user_service_client: UserServiceClientDependency,
) -> UserAggregator:
    return UserAggregator(user_repo, user_service_client)


UserAggregatorDependency = Annotated[UserAggregator, Depends(get_user_aggregator)]


def get_organization_aggregator(
    organization_repo: OrganizationRepositoryDependency,
    user_service_client: UserServiceClientDependency,
) -> OrganizationAggregator:
    return OrganizationAggregator(organization_repo, user_service_client)


OrganizationAggregatorDependency = Annotated[
    OrganizationAggregator, Depends(get_organization_aggregator)
]


async def get_event_repo(session: AsyncSessionDependency) -> EventsRepository:
    return EventsRepository(session)


EventsRepositoryDependency = Annotated[EventsRepository, Depends(get_event_repo)]


async def get_scraper_operation_history_repo(
    session: AsyncSessionDependency,
) -> ScraperOperationHistoryRepository:
    return ScraperOperationHistoryRepository(session)


ScraperOperationHistoryRepositoryDependency = Annotated[
    ScraperOperationHistoryRepository, Depends(get_scraper_operation_history_repo)
]


async def get_scraper_status_repo(
    session: AsyncSessionDependency,
) -> ScraperStatusRepository:
    return ScraperStatusRepository(
        session=session,
    )


ScraperStatusRepositoryDependency = Annotated[
    ScraperStatusRepository, Depends(get_scraper_status_repo)
]


async def get_reports_repo(session: AsyncSessionDependency) -> ReportsRepository:
    return ReportsRepository(session)


ReportsRepositoryDependency = Annotated[ReportsRepository, Depends(get_reports_repo)]


async def get_login_status_repo(
    session: AsyncSessionDependency,
) -> LoginStatusRepository:
    return LoginStatusRepository(session)


LoginStatusRepositoryDependency = Annotated[
    LoginStatusRepository, Depends(get_login_status_repo)
]


async def get_scraper_status_projection(
    session: AsyncSessionDependency,
) -> ScraperStatusProjection:
    return ScraperStatusProjection(
        session=session,
    )


ScraperStatusProjectionDependency = Annotated[
    ScraperStatusProjection, Depends(get_scraper_status_projection)
]


async def get_scraper_application(
    config: ConfigDependency,
    events_repository: EventsRepositoryDependency,
    scraper_status_repository: ScraperStatusRepositoryDependency,
    reports_repository: ReportsRepositoryDependency,
    login_status_repository: LoginStatusRepositoryDependency,
    scraper_history_repository: ScraperOperationHistoryRepositoryDependency,
    user_aggregator: UserAggregatorDependency,
    organization_aggregator: OrganizationAggregatorDependency,
) -> ScraperApplication:
    application = ScraperApplication(
        origin=config.origin,
        events_repository=events_repository,
        scraper_status_repository=scraper_status_repository,
    )

    async def get_by_org_user_source(repo, event):
        return await repo.get(
            organization_id=event.organization_id,
            user_id=event.user_id,
            source=event.body.source,
        )

    projections = [
        GenericProjection(
            repository=scraper_status_repository,
            event_types=[
                ScraperStateChangedEvent,
                LoginStateChangedEvent,
                ScraperBlockadeChangedEvent,
                ReportUploadedEvent,
            ],
            get_entity=get_by_org_user_source,
        ),
        GenericProjection(
            repository=scraper_history_repository,
            event_types=[ScraperStateChangedEvent, LoginStateChangedEvent],
            get_entity=lambda repo, event: repo.get_for(event),
        ),
        GenericProjection(
            repository=login_status_repository,
            event_types=[LoginStateChangedEvent],
            get_entity=get_by_org_user_source,
        ),
        GenericProjection(
            repository=reports_repository,
            event_types=[ReportUploadedEvent],
            get_entity=lambda repo, event: repo.get(event),
        ),
    ]

    for projection in projections:
        application.register(projection, *projection.event_types)

    application.register(user_aggregator, ScraperStateChangedEvent)
    application.register(organization_aggregator, ScraperStateChangedEvent)

    return application


ScraperApplicationDependency = Annotated[
    ScraperApplication, Depends(get_scraper_application)
]
