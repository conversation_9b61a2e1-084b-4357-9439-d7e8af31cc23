import jwt
from fastapi import HTT<PERSON>Exception
from starlette.status import HTTP_403_FORBIDDEN


class Unauthorized(HTTPException):
    def __init__(self, detail: str = "Not authenticated") -> None:
        super().__init__(status_code=HTTP_403_FORBIDDEN, detail=detail)


class TokenValidationError(Exception):
    def __init__(self, detail: str = "Invalid token"):
        super().__init__("TokenValidationError: " + detail)


def validate_token(token: str, public_key: str) -> None:
    try:
        jwt.decode(token, public_key, algorithms=["RS256"])
    except jwt.ExpiredSignatureError:
        # Ignore expired tokens to simplify the implementation
        pass
    except jwt.exceptions.DecodeError as e:
        raise TokenValidationError(repr(e))


def get_user_from_jwt(token: str, public_key: str):
    try:
        expired_time_margin = 60 * 60 * 3  # 3 hours in seconds

        user_from_jwt = jwt.decode(
            token,
            public_key,
            algorithms=["RS256"],
            leeway=expired_time_margin,
        )
        user_from_jwt["main_organization_id"] = next(
            organization["organization_id"]
            for organization in user_from_jwt["permissions"]
            if organization["name"]
            in {"DESKTOP_APP_LEGACY_PARENT", "DESKTOP_APP_LEGACY_CHILD"}
        )

    except jwt.ExpiredSignatureError:
        raise TokenValidationError("Token expired")
    except jwt.exceptions.DecodeError as e:
        raise TokenValidationError(str(e))

    except StopIteration:
        raise TokenValidationError("User does not have access to any organizations.")

    return user_from_jwt


# TODO: This method should be more clever but we do not have scraper only permissions.
def user_has_access_to_organization(current_user, organization_id):
    if current_user is None:
        return True
    accessible_organizations = [
        organization["organization_id"] for organization in current_user["permissions"]
    ]
    return organization_id in accessible_organizations
