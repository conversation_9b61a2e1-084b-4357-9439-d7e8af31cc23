from enum import Enum


class ApiTag(Enum):
    PUBLIC = "public"
    EXTERNAL = "external"
    INTERNAL = "internal"
    DEPRECATED = "deprecated"


tags_metadata = [
    {
        "name": ApiTag.PUBLIC.value,
        "description": "Public endpoints",
    },
    {
        "name": ApiTag.EXTERNAL.value,
        "description": "External endpoint. Require JWT for authentication",
    },
    {
        "name": ApiTag.INTERNAL.value,
        "description": "Internal endpoints for internal use only",
    },
]
