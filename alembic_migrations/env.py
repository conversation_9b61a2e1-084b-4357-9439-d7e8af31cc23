from logging.config import fileConfig

from alembic import context
from sqlalchemy import create_engine

from scraper_service.config import Config
from scraper_service.connectors.db import Base, setup_token_refresh
from scraper_service.logic.entities import DatabaseURL

# ruff: noqa: I001
# Import all models to ensure they are registered with the metadata
# This is needed for autogenerate option to work
from scraper_service.logic.events import *  # noqa: F403
from scraper_service.logic.repositories.login_status import *  # noqa: F403
from scraper_service.logic.repositories.organization_feature_flags import *  # noqa: F403
from scraper_service.logic.repositories.organization import *  # noqa: F403
from scraper_service.logic.repositories.reports import *  # noqa: F403
from scraper_service.logic.repositories.scraper_operation_history import *  # noqa: F403
from scraper_service.logic.repositories.scraper_status import *  # noqa: F403
from scraper_service.logic.repositories.user_feature_flags import *  # noqa: F403
from scraper_service.logic.repositories.user import *  # noqa: F403

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
alembic_config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if alembic_config.config_file_name is not None:
    fileConfig(alembic_config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

GRANT_PERMISSION_TO_DPT_QUERY = """GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO "DPT_TEAM";"""
GRANT_PERMISSION_TO_BACKUP_MANAGER_QUERY = (
    'GRANT USAGE ON SCHEMA public TO "id-sql-backup-{env}"; '
    'GRANT SELECT ON ALL TABLES IN SCHEMA public TO "id-sql-backup-{env}"; '
    'GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO "id-sql-backup-{env}";'
)
SET_OWNER_SCHEMA_ROLE = "SET ROLE indiebi_parent_role;"


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = alembic_config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """

    config = Config()
    config.database_url = DatabaseURL(
        alembic_config.get_main_option("sqlalchemy.url") or config.database_url
    )
    engine = create_engine(config.database_url)
    if "azure_identity_token" in config.database_url:
        setup_token_refresh(engine)

    with engine.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)
        if "azure_identity_token" in config.database_url:
            context.execute(SET_OWNER_SCHEMA_ROLE)

        with context.begin_transaction():
            context.run_migrations()

        if "azure_identity_token" in config.database_url:
            with context.begin_transaction():
                context.execute(GRANT_PERMISSION_TO_DPT_QUERY)
                context.execute(
                    GRANT_PERMISSION_TO_BACKUP_MANAGER_QUERY.format(env=config.env)
                )


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
