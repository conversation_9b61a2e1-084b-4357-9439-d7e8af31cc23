"""Add operation_id to event table

Revision ID: 138089b20e1d
Revises: 836a91441600
Create Date: 2024-12-09 19:53:49.688753

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "138089b20e1d"
down_revision: str | None = "836a91441600"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    op.add_column(
        "event", sa.Column("operation_id", sa.String(length=100), nullable=True)
    )


def downgrade() -> None:
    op.drop_column("event", "operation_id")
