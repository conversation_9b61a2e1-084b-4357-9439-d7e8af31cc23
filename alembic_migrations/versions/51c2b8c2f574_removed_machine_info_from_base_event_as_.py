"""Removed machine_info from base event as it is more of a body item

Revision ID: 51c2b8c2f574
Revises: 9a403fc014f3
Create Date: 2025-03-13 13:44:35.268801

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "51c2b8c2f574"
down_revision: str | None = "9a403fc014f3"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    op.drop_column("event", "machine_info")


def downgrade() -> None:
    op.add_column(
        "event",
        sa.Column(
            "machine_info",
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
    )
