"""login event

Revision ID: eef62647541c
Revises: 51c2b8c2f574
Create Date: 2025-04-23 15:28:44.878995

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "eef62647541c"
down_revision: str | None = "51c2b8c2f574"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "login_status_projection",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("organization_id", sa.String(length=100), nullable=False),
        sa.Column("source", sa.String(length=100), nullable=False),
        sa.Column("state", sa.String(length=100), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("is_manual_session", sa.Boolean(), nullable=True),
        sa.Column("consecutive_failed_login_count", sa.Integer(), nullable=False),
        sa.Column("last_success_timestamp", sa.DateTime(timezone=True), nullable=True),
        sa.Column("last_fail_timestamp", sa.DateTime(timezone=True), nullable=True),
        sa.Column("last_fail_reason", sa.String(length=100), nullable=True),
        sa.Column("user_id", sa.String(length=100), nullable=False),
        sa.Column("last_origin", sa.String(), nullable=True),
        sa.Column("version", sa.Integer(), nullable=False),
        sa.Column("last_operation_id", sa.String(length=100), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "user_id", "source", name="uq_login_status_projection_user_source"
        ),
    )

    op.add_column(
        "scraper_operation_history",
        sa.Column(
            "command", sa.String(length=100), server_default="SCRAPE", nullable=False
        ),
    )
    op.add_column(
        "scraper_operation_history",
        sa.Column("triggered_by", sa.String(length=100), nullable=True),
    )
    op.add_column(
        "scraper_operation_history",
        sa.Column("is_manual_session", sa.Boolean(), nullable=True),
    )

    op.add_column(
        "scraper_status_projection",
        sa.Column("uses_manual_session", sa.Boolean(), nullable=True),
    )
    op.add_column(
        "scraper_status_projection",
        sa.Column("triggered_by", sa.String(length=100), nullable=True),
    )
    op.add_column(
        "scraper_status_projection",
        sa.Column("last_command", sa.String(length=100), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("scraper_status_projection", "uses_manual_session")
    op.drop_column("scraper_status_projection", "last_command")
    op.drop_column("scraper_status_projection", "triggered_by")

    op.drop_column("scraper_operation_history", "is_manual_session")
    op.drop_column("scraper_operation_history", "command")
    op.drop_column("scraper_operation_history", "triggered_by")

    op.drop_table("login_status_projection")
    # ### end Alembic commands ###
