"""Initialize the event and projection table

Revision ID: 257c84fd0570
Revises:
Create Date: 2024-11-22 11:23:18.863348

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "257c84fd0570"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "event",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("event_type", sa.String(length=30), nullable=False),
        sa.Column("received_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("client_timestamp", sa.DateTime(timezone=True), nullable=False),
        sa.Column("user_id", sa.String(length=30), nullable=False),
        sa.Column("organization_id", sa.String(length=30), nullable=False),
        sa.Column("origin", sa.String(), nullable=False),
        sa.Column("body", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "scraper_status_projection",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("organization_id", sa.String(length=30), nullable=False),
        sa.Column("source", sa.String(length=30), nullable=False),
        sa.Column("state", sa.String(length=30), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("consecutive_failed_scrape_count", sa.Integer(), nullable=False),
        sa.Column("last_success_timestamp", sa.DateTime(timezone=True), nullable=True),
        sa.Column("last_fail_timestamp", sa.DateTime(timezone=True), nullable=True),
        sa.Column("last_user", sa.String(length=30), nullable=False),
        sa.Column("last_origin", sa.String(), nullable=True),
        sa.Column("version", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("organization_id", "source", name="uq_organization_source"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("scraper_status_projection")
    op.drop_table("event")
    # ### end Alembic commands ###
