"""Add machine info field to event

Revision ID: 9a403fc014f3
Revises: 2ae4d544ad4e
Create Date: 2025-03-11 16:16:53.903865

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "9a403fc014f3"
down_revision: str | None = "2ae4d544ad4e"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    op.add_column(
        "event",
        sa.Column(
            "machine_info", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
    )


def downgrade() -> None:
    op.drop_column("event", "machine_info")
