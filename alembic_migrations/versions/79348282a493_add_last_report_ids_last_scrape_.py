"""add last_report_ids, last_scrape_date_ranges columns to scraper_status_projection

Revision ID: 79348282a493
Revises: 19c247f15d68
Create Date: 2025-07-01 02:39:38.818770

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "79348282a493"
down_revision: str | None = "19c247f15d68"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_column("reports_projection", "report_ids")
    op.add_column(
        "reports_projection",
        sa.Column("report_ids", sa.ARRAY(sa.Integer()), nullable=True),
    )

    op.add_column(
        "scraper_status_projection",
        sa.Column("last_report_ids", sa.ARRAY(sa.Integer()), nullable=True),
    )
    op.add_column(
        "scraper_status_projection",
        sa.Column(
            "last_scrape_date_ranges",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("scraper_status_projection", "last_scrape_date_ranges")
    op.drop_column("scraper_status_projection", "last_report_ids")
    op.alter_column(
        "reports_projection",
        "report_ids",
        existing_type=sa.ARRAY(sa.Integer()),
        type_=postgresql.ARRAY(sa.VARCHAR(length=100)),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
