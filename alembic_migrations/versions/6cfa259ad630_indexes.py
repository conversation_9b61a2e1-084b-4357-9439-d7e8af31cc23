"""indexes

Revision ID: 6cfa259ad630
Revises: eef62647541c
Create Date: 2025-05-08 12:24:15.345967

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "6cfa259ad630"
down_revision: str | None = "eef62647541c"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "uq_login_status_projection_user_source",
        "login_status_projection",
        type_="unique",
    )
    op.create_index(
        "ix_login_status_projection_organization_id_user_id_source",
        "login_status_projection",
        ["organization_id", "user_id", "source"],
        unique=True,
    )
    op.create_index(
        op.f("ix_scraper_operation_history_organization_id"),
        "scraper_operation_history",
        ["organization_id"],
        unique=False,
    )
    op.create_unique_constraint(
        "uq_organization_source_operation",
        "scraper_operation_history",
        ["organization_id", "source", "operation_id"],
    )
    op.alter_column(
        "scraper_status_projection",
        column_name="last_user",
        new_column_name="user_id",
        existing_type=sa.String(length=100),
        existing_nullable=False,
    )
    op.drop_constraint(
        "uq_organization_source", "scraper_status_projection", type_="unique"
    )
    op.create_index(
        "ix_scraper_status_projection_organization_id_user_id_source",
        "scraper_status_projection",
        ["organization_id", "user_id", "source"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "scraper_status_projection",
        column_name="last_user",
        new_column_name="user_id",
        existing_type=sa.String(length=100),
        existing_nullable=False,
    )
    op.drop_index(
        "ix_scraper_status_projection_organization_id_user_id_source",
        table_name="scraper_status_projection",
    )
    op.create_unique_constraint(
        "uq_organization_source",
        "scraper_status_projection",
        ["organization_id", "source"],
    )
    op.drop_constraint(
        "uq_organization_source_operation", "scraper_operation_history", type_="unique"
    )
    op.drop_index(
        op.f("ix_scraper_operation_history_organization_id"),
        table_name="scraper_operation_history",
    )
    op.drop_index(
        "ix_login_status_projection_organization_id_user_id_source",
        table_name="login_status_projection",
    )
    op.create_unique_constraint(
        "uq_login_status_projection_user_source",
        "login_status_projection",
        ["user_id", "source"],
    )
    # ### end Alembic commands ###
