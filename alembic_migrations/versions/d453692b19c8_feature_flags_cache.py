"""Feature flags cache

Revision ID: d453692b19c8
Revises: 29e64cbb5e75
Create Date: 2025-08-13 12:30:09.144637

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "d453692b19c8"
down_revision: str | None = "29e64cbb5e75"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "organizations_feature_flags_cache",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("organization_id", sa.String(length=100), nullable=False),
        sa.Column(
            "feature_flags", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_organizations_feature_flags_cache_organization_id",
        "organizations_feature_flags_cache",
        ["organization_id"],
        unique=True,
    )
    op.create_table(
        "users_feature_flags_cache",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("user_id", sa.String(length=100), nullable=False),
        sa.Column(
            "feature_flags", postgresql.JSONB(astext_type=sa.Text()), nullable=False
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_users_feature_flags_cache_user_id",
        "users_feature_flags_cache",
        ["user_id"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        "ix_users_feature_flags_cache_user_id", table_name="users_feature_flags_cache"
    )
    op.drop_table("users_feature_flags_cache")
    op.drop_index(
        "ix_organizations_feature_flags_cache_organization_id",
        table_name="organizations_feature_flags_cache",
    )
    op.drop_table("organizations_feature_flags_cache")
    # ### end Alembic commands ###
