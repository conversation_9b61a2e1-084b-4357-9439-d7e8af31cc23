"""create statuses view

Revision ID: 29e64cbb5e75
Revises: 5e9c6376fb92
Create Date: 2025-07-17 19:47:29.973745

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "29e64cbb5e75"
down_revision: str | None = "5e9c6376fb92"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    op.rename_table("scraper_status_projection", "scraper_status")
    op.execute("""
        CREATE VIEW scraper_status_projection AS
        SELECT
            scraper_status.*,
            users_cache.email AS user_email,
            organizations_cache.name AS organization_name
        FROM
            scraper_status
        LEFT JOIN
            users_cache
        ON
            scraper_status.user_id = users_cache.user_id
        LEFT JOIN
            organizations_cache
        ON
            scraper_status.organization_id = organizations_cache.organization_id
    """)


def downgrade() -> None:
    op.execute("DROP VIEW IF EXISTS scraper_status_projection")
    op.rename_table("scraper_status", "scraper_status_projection")
