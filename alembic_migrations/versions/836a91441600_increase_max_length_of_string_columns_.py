"""Increase max length of string columns to 100

Revision ID: 836a91441600
Revises: 257c84fd0570
Create Date: 2024-12-05 21:04:02.059174

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "836a91441600"
down_revision: str | None = "257c84fd0570"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # Increase the length of string columns to 100
    op.alter_column(
        "event",
        "event_type",
        type_=sa.String(length=100),
        existing_type=sa.String(length=30),
        nullable=False,
    )
    op.alter_column(
        "event",
        "user_id",
        type_=sa.String(length=100),
        existing_type=sa.String(length=30),
        nullable=False,
    )
    op.alter_column(
        "event",
        "organization_id",
        type_=sa.String(length=100),
        existing_type=sa.String(length=30),
        nullable=False,
    )

    op.alter_column(
        "scraper_status_projection",
        "organization_id",
        type_=sa.String(length=100),
        existing_type=sa.String(length=30),
        nullable=False,
    )
    op.alter_column(
        "scraper_status_projection",
        "source",
        type_=sa.String(length=100),
        existing_type=sa.String(length=30),
        nullable=False,
    )
    op.alter_column(
        "scraper_status_projection",
        "state",
        type_=sa.String(length=100),
        existing_type=sa.String(length=30),
        nullable=False,
    )
    op.alter_column(
        "scraper_status_projection",
        "last_user",
        type_=sa.String(length=100),
        existing_type=sa.String(length=30),
        nullable=False,
    )


def downgrade() -> None:
    # Revert the length of string columns back to 30
    op.alter_column(
        "event",
        "event_type",
        type_=sa.String(length=30),
        existing_type=sa.String(length=100),
        nullable=False,
    )
    op.alter_column(
        "event",
        "user_id",
        type_=sa.String(length=30),
        existing_type=sa.String(length=100),
        nullable=False,
    )
    op.alter_column(
        "event",
        "organization_id",
        type_=sa.String(length=30),
        existing_type=sa.String(length=100),
        nullable=False,
    )

    op.alter_column(
        "scraper_status_projection",
        "organization_id",
        type_=sa.String(length=30),
        existing_type=sa.String(length=100),
        nullable=False,
    )
    op.alter_column(
        "scraper_status_projection",
        "source",
        type_=sa.String(length=30),
        existing_type=sa.String(length=100),
        nullable=False,
    )
    op.alter_column(
        "scraper_status_projection",
        "state",
        type_=sa.String(length=30),
        existing_type=sa.String(length=100),
        nullable=False,
    )
    op.alter_column(
        "scraper_status_projection",
        "last_user",
        type_=sa.String(length=30),
        existing_type=sa.String(length=100),
        nullable=False,
    )
