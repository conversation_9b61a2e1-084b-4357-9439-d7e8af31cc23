"""Add user service cache

Revision ID: 5e9c6376fb92
Revises: 79348282a493
Create Date: 2025-07-17 14:57:07.635102

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5e9c6376fb92"
down_revision: str | None = "79348282a493"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "organizations_cache",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("organization_id", sa.String(length=100), nullable=False),
        sa.Column("name", sa.String(length=100), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "ix_organizations_cache_organization_id",
        "organizations_cache",
        ["organization_id"],
        unique=True,
    )
    op.create_table(
        "users_cache",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("user_id", sa.String(length=100), nullable=False),
        sa.Column("email", sa.String(length=100), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("ix_users_cache_user_id", "users_cache", ["user_id"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_users_cache_user_id", table_name="users_cache")
    op.drop_table("users_cache")
    op.drop_index(
        "ix_organizations_cache_organization_id", table_name="organizations_cache"
    )
    op.drop_table("organizations_cache")
    # ### end Alembic commands ###
