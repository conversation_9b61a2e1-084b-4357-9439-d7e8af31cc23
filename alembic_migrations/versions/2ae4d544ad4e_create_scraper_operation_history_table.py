"""Create scraper_operation_history table

Revision ID: 2ae4d544ad4e
Revises: 8b2ce56d2a58
Create Date: 2024-12-10 23:07:13.435002

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "2ae4d544ad4e"
down_revision: str | None = "8b2ce56d2a58"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade():
    op.create_table(
        "scraper_operation_history",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column("organization_id", sa.String(length=100), nullable=False),
        sa.Column("operation_id", sa.String(length=100), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=sa.func.now(),
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            nullable=False,
            server_default=sa.func.now(),
        ),
        sa.Column("start_timestamp", sa.DateTime(timezone=True), nullable=True),
        sa.Column("end_timestamp", sa.DateTime(timezone=True), nullable=True),
        sa.Column("user_id", sa.String(length=100), nullable=False),
        sa.Column("source", sa.String(length=100), nullable=False),
        sa.Column("state", sa.String(length=100), nullable=False),
        sa.Column("fail_reason", sa.String(length=100), nullable=True),
    )


def downgrade():
    op.drop_table("scraper_operation_history")
