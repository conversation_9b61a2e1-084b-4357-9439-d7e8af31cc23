"""Add last_operation_id to scraper_status_projection table

Revision ID: 8b2ce56d2a58
Revises: 56fef6d36d24
Create Date: 2024-12-10 19:13:27.655228

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "8b2ce56d2a58"
down_revision: str | None = "56fef6d36d24"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade():
    op.add_column(
        "scraper_status_projection",
        sa.Column("last_operation_id", sa.String(length=100), nullable=True),
    )


def downgrade():
    op.drop_column("scraper_status_projection", "last_operation_id")
