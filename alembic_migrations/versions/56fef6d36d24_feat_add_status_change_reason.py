"""feat: add status change reason

Revision ID: 56fef6d36d24
Revises: 138089b20e1d
Create Date: 2024-12-10 14:26:09.912844

"""

from collections.abc import Sequence

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "56fef6d36d24"
down_revision: str | None = "138089b20e1d"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "scraper_status_projection",
        sa.Column("last_fail_reason", sa.String(length=100), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("scraper_status_projection", "last_fail_reason")
    # ### end Alembic commands ###
