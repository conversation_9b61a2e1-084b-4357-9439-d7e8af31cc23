# Test Load API with Locust

This README provides instructions on how to run Locust locally to test the load of your API.

## Prerequisites

Install all dependancies, including Locust

```bash
poetry install
```

## Running Locust

1. Write your new Locust test script in `locustfile.py` or use existing one.

2. Go to dir with `locustfile.py`

3. Start Locust:

   ```bash
   locust
   ```

4. Open your web browser and navigate to `http://localhost:8089`.

5. Configure the number of users and spawn rate, then start the test.

## Additional Resources

- [Locust Documentation](https://docs.locust.io/en/stable/)
