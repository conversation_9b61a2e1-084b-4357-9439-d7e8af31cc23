import random
import uuid
from datetime import UTC, datetime

from locust import HttpUser, between, task

organization_data: dict[str, list[str]] = {
    "o-001": [f"u-001-{j:03}" for j in range(10)],
    "o-002": [f"u-002-{j:03}" for j in range(10)],
    "o-003": [f"u-003-{j:03}" for j in range(10)],
    "o-004": [f"u-004-{j:03}" for j in range(10)],
    "o-005": [f"u-005-{j:03}" for j in range(10)],
    "o-006": [f"u-006-{j:03}" for j in range(10)],
    "o-007": [f"u-007-{j:03}" for j in range(10)],
    "o-008": [f"u-008-{j:03}" for j in range(10)],
    "o-009": [f"u-009-{j:03}" for j in range(10)],
    "o-010": [f"u-010-{j:03}" for j in range(10)],
}


class RegisterNewScraperEvent(HttpUser):
    host = "http://127.0.0.1:8000"
    wait_time = between(1, 2)

    def on_start(self):
        self.organization_id: str = random.choice(list(organization_data.keys()))
        self.user_id: str = random.choice(organization_data[self.organization_id])
        self.operation_id: str = str(uuid.uuid4())

    @task
    def register_new_scraper_event(self):
        statuses_sequence = ["SCHEDULED", "STARTED", "FINISHED"]
        for status in statuses_sequence:
            self.client.post(
                "/external/scraper_events",
                headers={"x-api-key": "ApiTest123!"},
                json={
                    "event_type": "scraper_state_changed",
                    "client_timestamp": datetime.now(tz=UTC).isoformat(),
                    "user_id": self.user_id,
                    "organization_id": self.organization_id,
                    "origin": "string",
                    "body": {
                        "new_state": status,
                        "source": "microsoft_sales",
                        "account_identifier": "string",
                        "reason": "string",
                    },
                    "operation_id": self.operation_id,
                },
            )
