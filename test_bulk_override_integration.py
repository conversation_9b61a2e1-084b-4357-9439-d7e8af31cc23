#!/usr/bin/env python3
"""
Integration test for the bulk_override method with real database.
"""

import asyncio
from sqlalchemy import select
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker

from scraper_service.logic.entities import UserFeatureFlags
from scraper_service.logic.repositories.user_feature_flags import (
    UserFeatureFlagsRepository,
    _UserFeatureFlagsModel,
)


async def test_bulk_override_integration():
    """Test the bulk_override method with real database."""
    
    # Create database connection
    engine = create_async_engine(
        "postgresql+psycopg://indiebi:Password1!@localhost:5432/scraper_service_db",
        echo=True  # Show SQL queries
    )
    
    async_session_maker = async_sessionmaker(engine, expire_on_commit=False)
    
    async with async_session_maker() as session:
        repo = UserFeatureFlagsRepository(session)
        
        print("🧪 Testing bulk_override method...")
        print("=" * 50)
        
        # Test 1: Add some initial data
        print("\n1️⃣ Adding initial test data...")
        initial_entities = [
            UserFeatureFlags(id="user1", feature_flags=["initial_flag1"]),
            UserFeatureFlags(id="user2", feature_flags=["initial_flag2"]),
        ]
        
        for entity in initial_entities:
            await repo.save(entity)
        await session.commit()
        
        # Verify initial data
        stmt = select(_UserFeatureFlagsModel)
        result = await session.execute(stmt)
        initial_count = len(result.scalars().all())
        print(f"   ✅ Added {initial_count} initial records")
        
        # Test 2: Use bulk_override to replace all data
        print("\n2️⃣ Testing bulk_override with new data...")
        new_entities = [
            UserFeatureFlags(id="new_user1", feature_flags=["flag1", "flag2"]),
            UserFeatureFlags(id="new_user2", feature_flags=["flag3"]),
            UserFeatureFlags(id="new_user3", feature_flags=["flag1", "flag4", "flag5"]),
        ]
        
        await repo.bulk_override(new_entities)
        await session.commit()
        
        # Verify the replacement worked
        stmt = select(_UserFeatureFlagsModel)
        result = await session.execute(stmt)
        all_entities = result.scalars().all()
        
        print(f"   ✅ Table now contains {len(all_entities)} records")
        
        # Check that old data is gone and new data is present
        user_ids = {entity.user_id for entity in all_entities}
        expected_user_ids = {"new_user1", "new_user2", "new_user3"}
        
        assert user_ids == expected_user_ids, f"Expected {expected_user_ids}, got {user_ids}"
        print(f"   ✅ User IDs match expected: {user_ids}")
        
        # Verify specific data
        for entity in all_entities:
            if entity.user_id == "new_user1":
                assert entity.feature_flags == ["flag1", "flag2"]
            elif entity.user_id == "new_user2":
                assert entity.feature_flags == ["flag3"]
            elif entity.user_id == "new_user3":
                assert entity.feature_flags == ["flag1", "flag4", "flag5"]
        
        print("   ✅ All feature flags data is correct")
        
        # Test 3: Use bulk_override with empty list (should clear all)
        print("\n3️⃣ Testing bulk_override with empty list...")
        await repo.bulk_override([])
        await session.commit()
        
        # Verify table is empty
        stmt = select(_UserFeatureFlagsModel)
        result = await session.execute(stmt)
        final_entities = result.scalars().all()
        
        assert len(final_entities) == 0, f"Expected empty table, got {len(final_entities)} records"
        print("   ✅ Table successfully cleared")
        
        print("\n🎉 All tests passed! The bulk_override method works correctly.")
        print("\nSummary:")
        print("- ✅ Atomically deletes all existing rows")
        print("- ✅ Bulk inserts new entities efficiently")
        print("- ✅ Handles empty input list correctly")
        print("- ✅ Maintains data integrity")
        print("- ✅ Uses database-optimal operations")


if __name__ == "__main__":
    asyncio.run(test_bulk_override_integration())
