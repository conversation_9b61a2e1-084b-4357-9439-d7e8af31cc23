api_key=ApiTest123!
database_url="postgresql+psycopg2://indiebi:Password1!@localhost:5432/scraper_service_db"

USER_SERVICE_URL="https://user-service-v2.indiebi.dev"

# Get value from "user-service-v2-api-key" secret in single click KV

USER_SERVICE_KEY="ibit-XYZ"

POETRY_AUTH_PATH="$HOME/.config/pypoetry/auth.toml"

# Please refresh this key if it is outdated.

# Extract the current value from the key vault, use single quotes and \n for new line: https://portal.azure.com/#view/Microsoft_Azure_KeyVault/ListObjectVersionsRBACBlade/~/overview/objectType/secrets/objectId/https%3A%2F%2Fkv-single-click-dev-b0.vault.azure.net%2Fsecrets%2Fuser-access-token-public-key/vaultResourceUri/%2Fsubscriptions%2F974be8a6-83ee-4087-b47d-7cd8424ba8e5%2FresourceGroups%2Frg-vault-single-click-dev%2Fproviders%2FMicrosoft.KeyVault%2Fvaults%2Fkv-single-click-dev-b0/vaultId/%2Fsubscriptions%2F974be8a6-83ee-4087-b47d-7cd8424ba8e5%2FresourceGroups%2Frg-vault-single-click-dev%2Fproviders%2FMicrosoft.KeyVault%2Fvaults%2Fkv-single-click-dev-b0/lifecycleState~/null

user_access_token_public_key='-----BEGIN PUBLIC KEY-----\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5j2QX6L/JFaMSBNLqNcU\nhMBpXSkuMpEjjRafDMl0oxfJT5XsAJaJj5ggKdSg+vPoSO6+0ThxcnKTocqiUoRZ\nMoqvOHJkOOyOWaN0juXkish6QXNPdqaQRz1uZxLv7YuDG9+nsZM5yHYOH9aDjbCP\nnzogTM6zHtYL9KQmCPLq+Tx7autjUeBE+OhKYxBikZV7HS7ObXzIHHrzAk/1lbj+\nNxGoE7M2+IGqmu2gNEtVFy7gdPneHE4S6eeLjh7iJgpRZqL24LTSrYDsnfiAY7RC\nr1ObSdEkmwwRvf0rj+WcdoSjnJ+jA6w9v25Kw4EAg4YBzOgpBAD0xWhFtW+HZg9Q\nff0qn+OHztoDnmPKsq1a+XY4StJFukDyV104l5BRQobiU0OKQFHm94Kb+nj4Kgbn\n07hEVvFmRhCpgeYenuegVaDl539hlbIUFBcNRCSNtqHAqWHCCDqZrMcFGhKjt8FI\n8s8cqeRZ11naFBKyhp9WrmVeDDvuWo/PJLKbt/APo6C6zRdbByvqs0zPP8y0ijYU\nkO4RnnB2EA4q8B7JTU7rVG9sDkdSuBYoawhFKQl9UP6nkMBagaBfO4bV/IrhEihS\nzYgMQbIdUnoT/uhrgtakOWsPFr1Qx7F9YbA7dHHfKtyIMA1FRcWYwSKLSVTQO2og\njr7xCCPN+S5gzwmsW44wll0CAwEAAQ==\n-----END PUBLIC KEY-----'
