"""
Example showing how to use enriched user data in projections.

This demonstrates how other projections can access enriched user and organization
data from the UserDataProjection.
"""

from datetime import datetime
from functools import singledispatchmethod

from pydantic import BaseModel, ConfigDict
from sqlalchemy import DateTime, String, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from scraper_service.connectors.db import Base
from scraper_service.logic.entities import OrganizationID, UserID
from scraper_service.logic.events.scraper_state_changed import ScraperStateChangedEvent
from scraper_service.logic.projections.base import Projection
from scraper_service.logic.services.user_data_lookup import UserDataLookupService


class EnrichedScraperEvent(BaseModel):
    """Example projection that includes enriched user data."""
    model_config = ConfigDict(from_attributes=True)

    user_id: UserID
    organization_id: OrganizationID
    event_type: str
    received_at: datetime
    
    # Enriched data from user service
    user_email: str | None = None
    organization_name: str | None = None

    @singledispatchmethod
    def apply(self, event) -> None:
        raise NotImplementedError(f"Event {event.__class__} not supported")

    @apply.register
    def _(self, event: ScraperStateChangedEvent) -> None:
        self.user_id = event.user_id
        self.organization_id = event.organization_id
        self.event_type = event.event_type
        self.received_at = event.received_at


class _EnrichedScraperEventModel(Base):
    __tablename__ = "enriched_scraper_events_example"

    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[str] = mapped_column(String(100))
    organization_id: Mapped[str] = mapped_column(String(100))
    event_type: Mapped[str] = mapped_column(String(100))
    received_at: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    user_email: Mapped[str | None] = mapped_column(String(255))
    organization_name: Mapped[str | None] = mapped_column(String(255))


class EnrichedScraperEventRepository:
    def __init__(self, session: AsyncSession) -> None:
        self._session = session

    async def save(self, enriched_event: EnrichedScraperEvent) -> None:
        new_entity = _EnrichedScraperEventModel(**enriched_event.model_dump())
        self._session.add(new_entity)
        await self._session.flush()


class EnrichedScraperEventProjection(Projection):
    """
    Example projection that demonstrates how to use enriched user data.
    
    This projection:
    1. Handles scraper events normally
    2. Looks up enriched user data from UserDataProjection
    3. Stores events with enriched data
    """

    def __init__(
        self, 
        repo: EnrichedScraperEventRepository,
        user_data_lookup: UserDataLookupService
    ) -> None:
        self._repo = repo
        self._user_data_lookup = user_data_lookup

    @singledispatchmethod
    async def handle(self, event) -> None:
        raise NotImplementedError

    @handle.register
    async def _(self, event: ScraperStateChangedEvent) -> None:
        # Create the basic event record
        enriched_event = EnrichedScraperEvent(
            user_id=event.user_id,
            organization_id=event.organization_id,
            event_type=event.event_type,
            received_at=event.received_at,
        )

        # Look up enriched data (this will be None if not yet enriched)
        user_email = await self._user_data_lookup.get_user_email(
            event.user_id, event.organization_id
        )
        organization_name = await self._user_data_lookup.get_organization_name(
            event.user_id, event.organization_id
        )

        # Add enriched data if available
        enriched_event.user_email = user_email
        enriched_event.organization_name = organization_name

        # Save the enriched event
        await self._repo.save(enriched_event)


# Usage in dependencies.py would be:
"""
async def get_enriched_scraper_event_repo(
    session: AsyncSessionDependency,
) -> EnrichedScraperEventRepository:
    return EnrichedScraperEventRepository(session)

async def get_user_data_lookup_service(
    user_data_repo: UserDataRepositoryDependency,
) -> UserDataLookupService:
    return UserDataLookupService(user_data_repo)

# In get_scraper_application():
application.register(
    EnrichedScraperEventProjection(
        enriched_scraper_event_repo, 
        user_data_lookup_service
    ),
    ScraperStateChangedEvent,
)
"""
