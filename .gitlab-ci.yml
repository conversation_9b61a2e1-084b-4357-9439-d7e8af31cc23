variables:
  POE<PERSON><PERSON>_CACHE_DIR: $CI_PROJECT_DIR/.poetry-cache
  POETRY_VIRTUALENVS_IN_PROJECT: "true"
  POETRY_HTTP_BASIC_INDIEBI_USERNAME: "gitlab-ci-token"
  POETRY_HTTP_BASIC_INDIEBI_PASSWORD: "$CI_JOB_TOKEN"
  CI_DEBUG_SERVICES: "true"
  DOCKER_TAG_SPECIFIC: $CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA

stages:
  - init
  - prepare
  - deploy
  - verify

.auto-main-auto-mr: &auto-main-auto-mr
  - if: $CI_MERGE_REQUEST_ID || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

.auto-main-manual-mr: &auto-main-manual-mr
  - if: $CI_MERGE_REQUEST_ID
    when: manual
    allow_failure: true
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

.manual-main: &manual-main
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    when: manual

.auto-main: &auto-main
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

build-image:
  stage: prepare
  dependencies: []
  image:
    name: crindiebimain.azurecr.io/dpt/image-builder:prod
  script:
    - image-exists && echo "Image already exists, skipping build!" && exit 0
    - image-builder
  tags:
    - dpt-azure
  rules: *auto-main-auto-mr

integration-test:
  tags:
    - dpt-azure
  stage: prepare
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-3.12-bookworm:prod
  services:
    - name: postgres:17.0
      alias: postgres_db
      variables:
        POSTGRES_USER: indiebi
        POSTGRES_PASSWORD: Password1!
        POSTGRES_DB: scraper_service_test
  variables:
    DATABASE_URL: "postgresql+psycopg2://indiebi:Password1!@postgres_db:5432/scraper_service_test"
  script:
    - poetry install
    - poetry run pytest --junitxml ${CI_PROJECT_DIR}/pytest.xml
  rules: *auto-main-auto-mr
  cache:
    key: global
    paths:
      - .poetry-cache
  artifacts:
    when: always
    reports:
      junit: ${CI_PROJECT_DIR}/pytest.xml

lints:
  stage: prepare
  image: crindiebimain.azurecr.io/dpt/ci-cd/python-poetry-3.12-bookworm:prod
  script:
    - poetry install
    - poetry run ruff check .
    - poetry run ruff format --check .
  rules: *auto-main-auto-mr
  cache:
    key: global
    paths:
      - .poetry-cache
  tags:
    - dpt-azure

.deploy:
  dependencies: []
  variables:
    GIT_STRATEGY: none
  stage: deploy
  image:
    name: crindiebimain.azurecr.io/dpt/image-builder:prod
  script:
    - tag-image $ENV
  allow_failure: false
  tags:
    - dpt-azure

deploy-dev:
  extends: .deploy
  variables:
    ENV: dev
  rules: *auto-main-manual-mr

deploy-prod:
  extends: .deploy
  variables:
    ENV: prod
  rules: *manual-main

.post-deploy-check:
 dependencies: []
 stage: verify
 image: alpine:latest
 timeout: 5m
 script: |
   apk add --no-cache curl jq
   until response=$(curl -s -w "\n%{http_code}" $HEALTH_URL) && [ "$(echo "$response" | tail -n1)" != "502" ] && echo "$response" | sed '$d' | jq -e --arg COMMIT $CI_COMMIT_SHORT_SHA '.docker.tag == "commit-"+$COMMIT' >/dev/null; do
     [ "$(echo "$response" | tail -n1)" = "502" ] && echo "Server is not responding - 502 error" || echo "⏳ Waiting for deployment..."
     sleep 3
   done
   echo "✅ Deployment verified - found matching commit"
 tags:
   - dpt-azure

post-deploy-check-dev:
  extends: .post-deploy-check
  variables:
    HEALTH_URL: https://scraper-service.indiebi.dev/health
  needs:
    - deploy-dev
  rules: *auto-main-auto-mr

post-deploy-check-prod:
  extends: .post-deploy-check
  variables:
    HEALTH_URL: https://scraper-service.indiebi.com/health
  needs:
    - deploy-prod
  rules: *auto-main
